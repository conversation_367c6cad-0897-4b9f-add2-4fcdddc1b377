# 每日扫描结果报告功能

## 功能概述

已成功为企业微信推送脚本添加了每日扫描结果报告功能。该功能可以：

1. **自动统计风险数据**：从测试数据库和正式数据库获取风险统计信息
2. **生成详细报告**：包含总风险数量、今日新增数量、漏洞类型排行等
3. **企业微信推送**：通过企业微信机器人自动推送报告

## 实现的功能

### 📊 数据统计
- 测试数据库和正式数据库的总风险数量
- 今日新增风险数量（根据create_time字段判断）
- 总漏洞类型数量排行前4名（根据risk_name分组）
- 今日新增漏洞类型数量排行前4名（数量为0的不显示）

### 🕙 扫描信息
- 扫描目标：228w ip资产
- 扫描时间：每天早上10:00:00

### 📱 推送格式
使用Markdown格式，包含：
- 基本信息（扫描目标、时间）
- 风险数据统计（测试库、正式库）
- 漏洞类型排行（总排行、今日新增排行）
- 安全提醒

## 文件结构

```
scan_tools/scan_utils/wechat_bot.py           # 核心功能实现
├── DailyReportGenerator                      # 报告生成器类
├── send_daily_scan_report()                  # 发送报告函数
└── 原有的企业微信推送功能

scripts/
├── simple_daily_report_sender.py            # 简化版发送脚本（推荐）
├── daily_report_sender.py                   # 完整版发送脚本（需要apscheduler）
├── demo_daily_report.py                     # 演示脚本
├── test_daily_report.py                     # 完整测试脚本
└── test_wechat_only.py                      # 企业微信测试脚本

docs/daily_report_usage.md                   # 详细使用文档
```

## 快速使用

### 1. 立即发送报告（推荐）

```bash
# 使用真实数据
python3 scripts/simple_daily_report_sender.py

# 使用模拟数据（测试）
python3 scripts/simple_daily_report_sender.py --mock
```

### 2. 演示功能

```bash
# 查看报告生成效果
python3 scripts/demo_daily_report.py
```

### 3. 在代码中调用

```python
from scan_tools.scan_utils.wechat_bot import send_daily_scan_report

# 发送真实数据报告
webhook_url = "your-webhook-url"
success = send_daily_scan_report(webhook_url)

# 发送模拟数据报告（测试）
success = send_daily_scan_report(webhook_url, use_mock_data=True)
```

## 数据库配置

### 测试数据库
- 主机: *************
- 数据库: web_scan
- 表名: dir_list_vul_all

### 正式数据库
- 主机: *************
- 数据库: hunyuan_sec_data
- 表名: dir_list_vul

## 报告示例

```markdown
📊 **每日安全扫描结果报告**

**扫描目标**: 228w ip资产
**扫描时间**: 2025-07-31 10:00:00

---

## 📈 风险数据统计

### 🧪 测试数据库
**总风险数量**: 1234 个
**今日新增**: 56 个

### 🏭 正式数据库  
**总风险数量**: 987 个
**今日新增**: 23 个

---

## 🔍 漏洞类型排行

### 📊 测试数据库总漏洞类型排行 (TOP4)
1. **redis未授权漏洞**: 456 个
2. **文件服务暴露漏洞**: 234 个
3. **ElasticSearch未授权访问**: 123 个
4. **ClickHouse未授权访问**: 89 个

### 📊 正式数据库总漏洞类型排行 (TOP4)
1. **redis未授权漏洞**: 345 个
2. **文件服务暴露漏洞**: 198 个
3. **ElasticSearch未授权访问**: 87 个
4. **ClickHouse未授权访问**: 65 个

---

## 🆕 今日新增漏洞类型排行 (TOP4)

### 🧪 测试数据库今日新增
1. **redis未授权漏洞**: 23 个
2. **文件服务暴露漏洞**: 12 个

### 🏭 正式数据库今日新增
1. **redis未授权漏洞**: 15 个
2. **文件服务暴露漏洞**: 8 个

---

⚠️ 请及时关注和处理相关安全风险！
```

## 定时任务设置

### 使用crontab

```bash
# 每天10:05执行
5 10 * * * cd /path/to/low_sec_detect && python3 scripts/simple_daily_report_sender.py
```

## 测试状态

✅ **已测试功能**：
- 报告生成功能（使用模拟数据）
- 企业微信推送功能
- 错误处理机制
- 命令行参数解析

⚠️ **需要实际环境测试**：
- 数据库连接（需要网络环境）
- 真实数据查询
- 定时任务执行

## 注意事项

1. **企业微信配置**：确保webhook地址正确
2. **数据库连接**：确保网络可达性和权限
3. **模拟数据模式**：用于测试，不依赖数据库
4. **日志记录**：所有操作都有详细日志
5. **错误处理**：包含完善的异常处理机制

## 下一步建议

1. 在实际环境中测试数据库连接
2. 配置正确的企业微信webhook地址
3. 设置定时任务（建议每天10:05执行）
4. 根据实际需求调整报告格式
5. 监控日志确保功能正常运行
