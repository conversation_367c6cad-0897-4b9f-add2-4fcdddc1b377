#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日扫描结果报告发送脚本
功能：
1. 从测试数据库和正式数据库获取风险统计数据
2. 生成每日扫描结果报告
3. 通过企业微信机器人发送报告
执行时间：建议每天早上10:05执行
"""

import os
import sys
import logging
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scan_tools.scan_utils.wechat_bot import send_daily_scan_report

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'logs', 'daily_report.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 企业微信机器人webhook地址
WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"

def send_daily_report(use_mock_data=False):
    """发送每日扫描结果报告"""
    try:
        if use_mock_data:
            logger.info("开始生成并发送每日扫描结果报告（使用模拟数据）...")
        else:
            logger.info("开始生成并发送每日扫描结果报告...")

        # 发送报告
        success = send_daily_scan_report(WEBHOOK_URL, use_mock_data=use_mock_data)

        if success:
            logger.info("每日扫描结果报告发送成功")
        else:
            logger.error("每日扫描结果报告发送失败")

    except Exception as e:
        logger.error(f"发送每日报告时发生异常: {str(e)}")

def run_scheduler():
    """运行定时任务调度器"""
    scheduler = BlockingScheduler()
    
    # 每天早上10:05执行
    scheduler.add_job(
        send_daily_report,
        'cron',
        hour=10,
        minute=5,
        id='daily_report_job'
    )
    
    logger.info("每日报告定时任务已启动，将在每天10:05执行")
    logger.info("按 Ctrl+C 停止定时任务")
    
    try:
        scheduler.start()
    except KeyboardInterrupt:
        logger.info("定时任务已停止")
        scheduler.shutdown()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='每日扫描结果报告发送脚本')
    parser.add_argument('--now', action='store_true', help='立即发送报告（不使用定时任务）')
    parser.add_argument('--schedule', action='store_true', help='启动定时任务')
    parser.add_argument('--mock', action='store_true', help='使用模拟数据（用于测试）')

    args = parser.parse_args()

    if args.now:
        # 立即发送报告
        send_daily_report(use_mock_data=args.mock)
    elif args.schedule:
        # 启动定时任务
        run_scheduler()
    else:
        # 默认立即发送报告
        print("使用方式:")
        print("  python daily_report_sender.py --now         # 立即发送报告")
        print("  python daily_report_sender.py --now --mock  # 立即发送报告（模拟数据）")
        print("  python daily_report_sender.py --schedule    # 启动定时任务")
        print("\n默认执行立即发送报告...")
        send_daily_report(use_mock_data=args.mock)
