#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
定时任务脚本：从DEBUG数据库拉取当天数据并推送到PROD数据库
执行时间：每天早上10:10
功能：
1. 从DEBUG数据库拉取当天的数据（根据create_time字段）
2. 只拉取risk_type <= 4的数据
3. 将数据推送到PROD数据库
"""

import os
import sys
import pymysql
import logging
from datetime import datetime, date
from typing import List, Dict, Any
from apscheduler.schedulers.blocking import BlockingScheduler

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.common_config import DB_CONFIG, DB_TABLE_NAME

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'supervisor_config', 'daily_data_sync.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataSyncHandler:
    """数据同步处理器"""
    
    def __init__(self):
        # DEBUG数据库配置
        self.debug_db_config = {
            'host': '*************',
            'user': 'root',
            'password': 'Tegsec_09040',
            'database': 'web_scan',
            'charset': 'utf8mb4'
        }
        self.debug_table_name = 'dir_list_vul_all'
        
        # PROD数据库配置
        self.prod_db_config = {
            'host': '*************',
            'user': 'hunyuan_staff',
            'password': 'hunyuan_staff0820',
            'database': 'hunyuan_sec_data',
            'charset': 'utf8mb4'
        }
        self.prod_table_name = 'dir_list_vul'
    
    def get_connection(self, db_config: dict):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def fetch_today_data_from_debug(self) -> List[Dict[str, Any]]:
        """从DEBUG数据库拉取当天的数据"""
        today_str = date.today().strftime('%Y-%m-%d')
        logger.info(f"开始从DEBUG数据库拉取 {today_str} 的数据...")
        
        connection = None
        try:
            connection = self.get_connection(self.debug_db_config)
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 查询当天的数据，且risk_type <= 4
                sql = f"""
                    SELECT * FROM {self.debug_table_name}
                    WHERE DATE(create_time) = %s 
                    AND risk_type <= 4
                    ORDER BY create_time ASC
                """
                cursor.execute(sql, (today_str,))
                results = cursor.fetchall()
                
                logger.info(f"从DEBUG数据库成功拉取到 {len(results)} 条符合条件的数据")
                return results
                
        except Exception as e:
            logger.error(f"从DEBUG数据库拉取数据失败: {e}")
            return []
        finally:
            if connection:
                connection.close()
    
    def check_record_exists_in_prod(self, record: Dict[str, Any]) -> bool:
        """检查记录是否已存在于PROD数据库中"""
        connection = None
        try:
            connection = self.get_connection(self.prod_db_config)
            
            with connection.cursor() as cursor:
                # 根据关键字段检查是否存在重复记录
                sql = f"""
                    SELECT COUNT(*) FROM {self.prod_table_name}
                    WHERE host = %s AND port = %s AND risk_type = %s AND url = %s
                """
                cursor.execute(sql, (
                    record['host'], 
                    record['port'], 
                    record['risk_type'], 
                    record['url']
                ))
                count = cursor.fetchone()[0]
                return count > 0
                
        except Exception as e:
            logger.error(f"检查记录是否存在时出错: {e}")
            return False
        finally:
            if connection:
                connection.close()
    
    def insert_record_to_prod(self, record: Dict[str, Any]) -> bool:
        """将单条记录插入到PROD数据库"""
        connection = None
        try:
            connection = self.get_connection(self.prod_db_config)
            
            with connection.cursor() as cursor:
                # 构建插入SQL
                columns = list(record.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                columns_str = ', '.join(columns)
                
                sql = f"""
                    INSERT INTO {self.prod_table_name} ({columns_str})
                    VALUES ({placeholders})
                """
                
                values = list(record.values())
                cursor.execute(sql, values)
                connection.commit()
                return True
                
        except Exception as e:
            logger.error(f"插入记录到PROD数据库失败: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if connection:
                connection.close()
    
    def sync_data_to_prod(self, records: List[Dict[str, Any]]) -> Dict[str, int]:
        """将数据同步到PROD数据库"""
        if not records:
            logger.info("没有需要同步的数据")
            return {'total': 0, 'inserted': 0, 'skipped': 0, 'failed': 0}
        
        logger.info(f"开始将 {len(records)} 条记录同步到PROD数据库...")
        
        stats = {'total': len(records), 'inserted': 0, 'skipped': 0, 'failed': 0}
        
        for i, record in enumerate(records, 1):
            try:
                # 检查记录是否已存在
                if self.check_record_exists_in_prod(record):
                    logger.debug(f"记录 {i}/{len(records)} 已存在，跳过: {record['host']}:{record['port']}")
                    stats['skipped'] += 1
                    continue
                
                # 插入新记录
                if self.insert_record_to_prod(record):
                    logger.debug(f"记录 {i}/{len(records)} 插入成功: {record['host']}:{record['port']}")
                    stats['inserted'] += 1
                else:
                    logger.warning(f"记录 {i}/{len(records)} 插入失败: {record['host']}:{record['port']}")
                    stats['failed'] += 1
                    
            except Exception as e:
                logger.error(f"处理记录 {i}/{len(records)} 时出错: {e}")
                stats['failed'] += 1
        
        logger.info(f"数据同步完成 - 总计: {stats['total']}, 插入: {stats['inserted']}, 跳过: {stats['skipped']}, 失败: {stats['failed']}")
        return stats
    
    def run_daily_sync(self):
        """执行每日数据同步任务"""
        logger.info("="*50)
        logger.info("开始执行每日数据同步任务")
        logger.info("="*50)
        
        try:
            # 1. 从DEBUG数据库拉取当天数据
            debug_records = self.fetch_today_data_from_debug()
            
            if not debug_records:
                logger.info("DEBUG数据库中没有当天的数据需要同步")
                return
            
            # 2. 同步数据到PROD数据库
            sync_stats = self.sync_data_to_prod(debug_records)
            
            # 3. 记录同步结果
            logger.info("每日数据同步任务完成")
            logger.info(f"同步统计: {sync_stats}")
            
        except Exception as e:
            logger.error(f"每日数据同步任务执行失败: {e}")
            raise

def main():
    """主函数 - 设置定时任务"""
    logger.info("启动每日数据同步定时任务服务...")
    
    # 创建数据同步处理器
    sync_handler = DataSyncHandler()
    
    # 创建调度器
    scheduler = BlockingScheduler()
    
    # 添加定时任务：每天早上10:10执行
    scheduler.add_job(
        sync_handler.run_daily_sync,
        'cron',
        hour=10,
        minute=10,
        id='daily_data_sync',
        name='每日数据同步任务'
    )
    
    logger.info("定时任务已设置：每天早上10:10执行数据同步")
    logger.info("服务启动中...")
    
    try:
        # 启动调度器
        scheduler.start()
    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在关闭服务...")
        scheduler.shutdown()
        logger.info("服务已停止")

if __name__ == "__main__":
    # 确保日志目录存在
    logs_dir = os.path.join(project_root, 'logs')
    os.makedirs(logs_dir, exist_ok=True)
    
    main()
