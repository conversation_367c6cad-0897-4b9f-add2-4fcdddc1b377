#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
from typing import List, <PERSON><PERSON>


def read_ip_file(file_path: str) -> List[str]:
    """
    读取IP文件，返回IP列表
    
    Args:
        file_path: IP文件路径
        
    Returns:
        IP地址列表
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    ips = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            ip = line.strip()
            if ip:  # 跳过空行
                ips.append(ip)
    
    return ips


def split_ips_by_ratio(ips: List[str], ratio: Tuple[int, int] = (50, 50)) -> Tuple[List[str], List[str]]:
    """
    按照指定比例拆分IP列表
    
    Args:
        ips: IP地址列表
        ratio: 拆分比例，默认为(50, 50)
        
    Returns:
        三个IP列表的元组
    """
    total_ips = len(ips)
    if total_ips == 0:
        return [], []
    
    # 计算每个部分的数量
    ratio_sum = sum(ratio)
    node1_count = int(total_ips * ratio[0] / ratio_sum)
    node2_count = total_ips - node1_count
    # master_count = total_ips - node1_count - node2_count  # 剩余的都给master
    
    # 拆分IP列表
    node1_ips = ips[:node1_count]
    node2_ips = ips[node1_count:node1_count + node2_count]
    # master_ips = ips[node1_count + node2_count:]
    
    return node1_ips, node2_ips


def write_ip_file(ips: List[str], file_path: str) -> None:
    """
    将IP列表写入文件
    
    Args:
        ips: IP地址列表
        file_path: 输出文件路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        for ip in ips:
            f.write(f"{ip}\n")


def split_ip_file(input_file: str, output_dir: str = None, ratio: Tuple[int, int] = (50, 50)) -> None:
    """
    拆分IP文件的主函数
    
    Args:
        input_file: 输入IP文件路径
        output_dir: 输出目录，如果为None则使用输入文件所在目录
        ratio: 拆分比例，默认为(50, 50)
    """
    print(f"开始处理文件: {input_file}")
    
    # 读取IP文件
    ips = read_ip_file(input_file)
    print(f"总共读取到 {len(ips)} 个IP地址")
    
    # 按比例拆分
    node1_ips, node2_ips = split_ips_by_ratio(ips, ratio)
    print(f"拆分结果: node1={len(node1_ips)}, node2={len(node2_ips)}")
    
    # 确定输出目录
    if output_dir is None:
        output_dir = os.path.dirname(input_file)
    
    # 输出文件路径
    node1_file = os.path.join(output_dir, 'merged_ip_node_1.txt')
    node2_file = os.path.join(output_dir, 'merged_ip_node_2.txt')
    # master_file = os.path.join(output_dir, 'merged_ip_master.txt')
    
    # 写入文件
    write_ip_file(node1_ips, node1_file)
    write_ip_file(node2_ips, node2_file)
    # write_ip_file(master_ips, master_file)
    
    print(f"拆分完成:")
    print(f"  - {node1_file}: {len(node1_ips)} 个IP")
    print(f"  - {node2_file}: {len(node2_ips)} 个IP")
    # print(f"  - {master_file}: {len(master_ips)} 个IP")


def main():
    """主函数"""
    input_file = '../temp_ip_info_assets/merged_ip.txt'
    ratio = [50, 50]
    output_dir = '../temp_ip_info_assets'
    
    try:
        split_ip_file(input_file, output_dir, tuple(ratio))
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
