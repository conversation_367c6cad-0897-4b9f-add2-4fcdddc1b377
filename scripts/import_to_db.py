import pandas as pd
import pymysql
from datetime import datetime

# --- 数据库配置 ---
# 再次提醒：在生产环境中，请避免将密码直接写入代码。
# 推荐使用环境变量、加密配置文件或密钥管理服务来保障安全。
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Tegsec_09040', # 请使用您的真实密码
    'database': 'web_scan',
    'charset': 'utf8mb4',
}

# --- 数据库表名 ---
DB_TABLE_NAME = 'dir_list_common'

def write_csv_to_db_with_pymysql(csv_file_path):
    """
    使用 pymysql 读取CSV文件并将其内容写入到MySQL数据库中。

    参数:
    csv_file_path (str): 输入的CSV文件的路径。
    """
    conn = None  # 初始化连接变量
    try:
        # 1. 读取CSV文件到Pandas DataFrame
        df = pd.read_csv(csv_file_path)
        print(f"成功读取CSV文件: {csv_file_path}，共 {len(df)} 条记录。")

        # 2. 建立数据库连接 (使用 pymysql)
        conn = pymysql.connect(**DB_CONFIG)
        print("成功连接到数据库 (使用 pymysql)。")

        # 使用 with 语句可以确保 cursor 被正确关闭
        with conn.cursor() as cursor:
            # 3. 准备插入语句
            insert_query = f"""
                INSERT INTO {DB_TABLE_NAME} (
                    date_key, leak_type, url, host, port, user_name, bg, dept,
                    center, risk_name, risk_desc, risk_tag, risk_level,
                    risk_status, oa_group, create_by, case_content, create_time, risk_type
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """

            # 4. 遍历DataFrame，准备插入数据
            records_to_insert = []
            current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            current_date_key = datetime.now().strftime('%Y%m%d')

            for index, row in df.iterrows():
                # 使用 .get(key, default_value) 来安全地获取数据，防止因列不存在而报错
                record = (
                    current_date_key,                 # date_key
                    '目录遍历',                         # leak_type
                    row.get('url', ''),               # url (假设CSV中可能有这一列)
                    row.get('ip', ''),                # host (从CSV的'ip'列获取)
                    row.get('port', 80),              # port (假设CSV中可能有，否则默认为80)
                    row.get('user_name', ''),         # user_name
                    row.get('bg', ''),                # bg
                    row.get('dept', ''),              # dept
                    row.get('center', ''),            # center
                    '默认风险',                         # risk_name
                    '默认描述',                         # risk_desc
                    '默认标签',                         # risk_tag
                    '中危',                           # risk_level
                    '待处理',                         # risk_status
                    row.get('oa_group', ''),          # oa_group
                    'python_script_pymysql',          # create_by
                    '',                               # case_content
                    current_time_str,                 # create_time
                    0                                 # risk_type (int类型，默认为0)
                )
                records_to_insert.append(record)

            # 5. 执行批量插入
            cursor.executemany(insert_query, records_to_insert)
            
        # 6. 提交事务
        conn.commit()
        print(f"成功提交 {len(records_to_insert)} 条记录到表 '{DB_TABLE_NAME}' 中。")

    except FileNotFoundError:
        print(f"错误: CSV文件未找到 - '{csv_file_path}'")
    except pd.errors.EmptyDataError:
        print(f"错误: CSV文件 '{csv_file_path}' 为空，没有数据可以插入。")
    except pymysql.Error as err:
        print(f"数据库错误: {err}")
        if conn:
            conn.rollback() # 如果出错，回滚所有更改
            print("数据库操作已回滚。")
    except KeyError as e:
        print(f"错误: CSV文件中缺少必要的列 - {e}")
    except Exception as e:
        print(f"发生了一个未知错误: {e}")
    finally:
        # 7. 关闭数据库连接
        if conn:
            conn.close()
            print("数据库连接已关闭。")

# --- 主程序 ---
if __name__ == "__main__":
    # 请将这里的 'filtered_ip_info.csv' 替换为您实际的CSV文件名
    input_csv_file = 'filtered_ip_info.csv' 
    write_csv_to_db_with_pymysql(input_csv_file)