#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接测试脚本
用于验证DEBUG和PROD数据库的连接是否正常
"""

import os
import sys
import pymysql

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_debug_db():
    """测试DEBUG数据库连接"""
    print("测试DEBUG数据库连接...")
    
    debug_config = {
        'host': '*************',
        'user': 'root',
        'password': 'Tegsec_09040',
        'database': 'web_scan',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**debug_config)
        print("✓ DEBUG数据库连接成功")
        
        with connection.cursor() as cursor:
            # 测试查询表结构
            cursor.execute("DESCRIBE dir_list_vul_all")
            columns = cursor.fetchall()
            print(f"✓ dir_list_vul_all表有 {len(columns)} 个字段")
            
            # 测试查询数据量
            cursor.execute("SELECT COUNT(*) FROM dir_list_vul_all WHERE risk_type <= 4")
            count = cursor.fetchone()[0]
            print(f"✓ 符合条件的记录数: {count}")
            
            # 测试查询今天的数据
            cursor.execute("SELECT COUNT(*) FROM dir_list_vul_all WHERE DATE(create_time) = CURDATE() AND risk_type <= 4")
            today_count = cursor.fetchone()[0]
            print(f"✓ 今天的符合条件记录数: {today_count}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ DEBUG数据库连接失败: {e}")
        return False

def test_prod_db():
    """测试PROD数据库连接"""
    print("\n测试PROD数据库连接...")
    
    prod_config = {
        'host': '*************',
        'user': 'hunyuan_staff',
        'password': 'hunyuan_staff0820',
        'database': 'hunyuan_sec_data',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**prod_config)
        print("✓ PROD数据库连接成功")
        
        with connection.cursor() as cursor:
            # 测试查询表结构
            cursor.execute("DESCRIBE dir_list_vul")
            columns = cursor.fetchall()
            print(f"✓ dir_list_vul表有 {len(columns)} 个字段")
            
            # 测试查询数据量
            cursor.execute("SELECT COUNT(*) FROM dir_list_vul")
            count = cursor.fetchone()[0]
            print(f"✓ 当前记录数: {count}")
            
            # 测试查询今天的数据
            cursor.execute("SELECT COUNT(*) FROM dir_list_vul WHERE DATE(create_time) = CURDATE()")
            today_count = cursor.fetchone()[0]
            print(f"✓ 今天的记录数: {today_count}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ PROD数据库连接失败: {e}")
        return False

def compare_table_structures():
    """比较两个表的结构"""
    print("\n比较表结构...")
    
    debug_config = {
        'host': '*************',
        'user': 'root',
        'password': 'Tegsec_09040',
        'database': 'web_scan',
        'charset': 'utf8mb4'
    }
    
    prod_config = {
        'host': '*************',
        'user': 'hunyuan_staff',
        'password': 'hunyuan_staff0820',
        'database': 'hunyuan_sec_data',
        'charset': 'utf8mb4'
    }
    
    try:
        # 获取DEBUG表结构
        debug_conn = pymysql.connect(**debug_config)
        with debug_conn.cursor() as cursor:
            cursor.execute("DESCRIBE dir_list_vul_all")
            debug_columns = {row[0]: row[1] for row in cursor.fetchall()}
        debug_conn.close()
        
        # 获取PROD表结构
        prod_conn = pymysql.connect(**prod_config)
        with prod_conn.cursor() as cursor:
            cursor.execute("DESCRIBE dir_list_vul")
            prod_columns = {row[0]: row[1] for row in cursor.fetchall()}
        prod_conn.close()
        
        # 比较字段
        debug_fields = set(debug_columns.keys())
        prod_fields = set(prod_columns.keys())
        
        common_fields = debug_fields & prod_fields
        debug_only = debug_fields - prod_fields
        prod_only = prod_fields - debug_fields
        
        print(f"✓ 共同字段: {len(common_fields)} 个")
        print(f"  {sorted(common_fields)}")
        
        if debug_only:
            print(f"⚠ DEBUG表独有字段: {sorted(debug_only)}")
        
        if prod_only:
            print(f"⚠ PROD表独有字段: {sorted(prod_only)}")
        
        # 检查关键字段
        key_fields = ['host', 'port', 'risk_type', 'url', 'create_time']
        missing_fields = []
        for field in key_fields:
            if field not in common_fields:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"✗ 缺少关键字段: {missing_fields}")
            return False
        else:
            print("✓ 所有关键字段都存在")
            return True
            
    except Exception as e:
        print(f"✗ 表结构比较失败: {e}")
        return False

def main():
    """主函数"""
    print("="*50)
    print("数据库连接测试")
    print("="*50)
    
    debug_ok = test_debug_db()
    prod_ok = test_prod_db()
    structure_ok = compare_table_structures()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"DEBUG数据库: {'✓ 正常' if debug_ok else '✗ 异常'}")
    print(f"PROD数据库: {'✓ 正常' if prod_ok else '✗ 异常'}")
    print(f"表结构兼容: {'✓ 兼容' if structure_ok else '✗ 不兼容'}")
    
    if debug_ok and prod_ok and structure_ok:
        print("\n🎉 所有测试通过，可以开始数据同步！")
        return True
    else:
        print("\n❌ 存在问题，请检查后再进行数据同步")
        return False

if __name__ == "__main__":
    main()
