#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日扫描结果报告演示脚本
使用模拟数据演示报告功能，不依赖数据库连接
"""

import os
import sys

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scan_tools.scan_utils.wechat_bot import DailyReportGenerator, send_daily_scan_report

def demo_report_generation():
    """演示报告生成功能"""
    print("=" * 60)
    print("演示每日扫描结果报告生成功能")
    print("=" * 60)
    
    generator = DailyReportGenerator()
    
    print("正在生成模拟数据报告...")
    report_content = generator.generate_daily_report(use_mock_data=True)
    
    print("\n生成的报告内容:")
    print("-" * 60)
    print(report_content)
    print("-" * 60)
    
    return report_content

def demo_wechat_sending():
    """演示企业微信发送功能"""
    print("\n" + "=" * 60)
    print("演示企业微信发送功能")
    print("=" * 60)
    
    # 企业微信机器人webhook地址
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"
    
    response = input("是否要发送演示报告到企业微信？(y/N): ")
    if response.lower() != 'y':
        print("跳过企业微信发送演示")
        return
    
    print("正在发送演示报告...")
    try:
        success = send_daily_scan_report(webhook_url, use_mock_data=True)
        if success:
            print("✅ 演示报告发送成功！")
        else:
            print("❌ 演示报告发送失败！")
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    
    instructions = """
1. 立即发送报告（使用真实数据）:
   python3 scripts/daily_report_sender.py --now

2. 启动定时任务:
   python3 scripts/daily_report_sender.py --schedule

3. 测试功能（包含数据库连接测试）:
   python3 scripts/test_daily_report.py

4. 仅测试企业微信功能:
   python3 scripts/test_wechat_only.py

5. 演示功能（使用模拟数据）:
   python3 scripts/demo_daily_report.py

配置文件位置:
- 核心功能: scan_tools/scan_utils/wechat_bot.py
- 发送脚本: scripts/daily_report_sender.py
- 使用文档: docs/daily_report_usage.md

注意事项:
- 确保企业微信webhook地址配置正确
- 确保数据库连接配置正确
- 建议先使用演示模式测试功能
"""
    print(instructions)

def main():
    """主函数"""
    print("每日扫描结果报告功能演示")
    
    # 演示报告生成
    report_content = demo_report_generation()
    
    # 演示企业微信发送
    demo_wechat_sending()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n✅ 演示完成！")

if __name__ == "__main__":
    main()
