#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：用于手动测试数据同步功能
可以指定日期进行测试，而不仅限于当天
"""

import os
import sys
import argparse
from datetime import datetime, date

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

import logging
import pymysql
from scripts.daily_data_sync import DataSyncHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestDataSyncHandler(DataSyncHandler):
    """测试用的数据同步处理器，支持指定日期"""
    
    def fetch_data_by_date(self, target_date: str):
        """从DEBUG数据库拉取指定日期的数据"""
        logger.info(f"开始从DEBUG数据库拉取 {target_date} 的数据...")
        
        connection = None
        try:
            connection = self.get_connection(self.debug_db_config)
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 查询指定日期的数据，且risk_type <= 4
                sql = f"""
                    SELECT * FROM {self.debug_table_name}
                    WHERE DATE(create_time) = %s 
                    AND risk_type <= 4
                    ORDER BY create_time ASC
                """
                cursor.execute(sql, (target_date,))
                results = cursor.fetchall()
                
                logger.info(f"从DEBUG数据库成功拉取到 {len(results)} 条符合条件的数据")
                return results
                
        except Exception as e:
            logger.error(f"从DEBUG数据库拉取数据失败: {e}")
            return []
        finally:
            if connection:
                connection.close()
    
    def test_sync_by_date(self, target_date: str):
        """测试指定日期的数据同步"""
        logger.info("="*50)
        logger.info(f"开始测试 {target_date} 的数据同步")
        logger.info("="*50)
        
        try:
            # 1. 从DEBUG数据库拉取指定日期数据
            debug_records = self.fetch_data_by_date(target_date)
            
            if not debug_records:
                logger.info(f"DEBUG数据库中没有 {target_date} 的数据需要同步")
                return
            
            # 2. 显示数据概览
            logger.info("数据概览:")
            risk_type_stats = {}
            for record in debug_records:
                risk_type = record['risk_type']
                risk_type_stats[risk_type] = risk_type_stats.get(risk_type, 0) + 1
            
            for risk_type, count in sorted(risk_type_stats.items()):
                logger.info(f"  risk_type {risk_type}: {count} 条记录")
            
            # 3. 询问是否继续同步
            response = input(f"\n是否继续将这 {len(debug_records)} 条记录同步到PROD数据库? (y/N): ")
            if response.lower() != 'y':
                logger.info("用户取消同步操作")
                return
            
            # 4. 同步数据到PROD数据库
            sync_stats = self.sync_data_to_prod(debug_records)
            
            # 5. 记录同步结果
            logger.info(f"{target_date} 数据同步测试完成")
            logger.info(f"同步统计: {sync_stats}")
            
        except Exception as e:
            logger.error(f"数据同步测试失败: {e}")
            raise
    
    def show_debug_data_summary(self, days: int = 7):
        """显示DEBUG数据库最近几天的数据概览"""
        logger.info(f"DEBUG数据库最近 {days} 天的数据概览:")
        
        connection = None
        try:
            connection = self.get_connection(self.debug_db_config)
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"""
                    SELECT 
                        DATE(create_time) as date,
                        risk_type,
                        COUNT(*) as count
                    FROM {self.debug_table_name}
                    WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
                    AND risk_type <= 4
                    GROUP BY DATE(create_time), risk_type
                    ORDER BY date DESC, risk_type ASC
                """
                cursor.execute(sql, (days,))
                results = cursor.fetchall()
                
                if not results:
                    logger.info("  没有找到数据")
                    return
                
                current_date = None
                for row in results:
                    if row['date'] != current_date:
                        current_date = row['date']
                        logger.info(f"\n  {current_date}:")
                    logger.info(f"    risk_type {row['risk_type']}: {row['count']} 条")
                
        except Exception as e:
            logger.error(f"查询数据概览失败: {e}")
        finally:
            if connection:
                connection.close()

def main():
    parser = argparse.ArgumentParser(description='测试数据同步功能')
    parser.add_argument('--date', type=str, help='指定同步日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--summary', action='store_true', help='显示最近7天的数据概览')
    parser.add_argument('--today', action='store_true', help='同步今天的数据')
    
    args = parser.parse_args()
    
    # 创建测试处理器
    test_handler = TestDataSyncHandler()
    
    if args.summary:
        test_handler.show_debug_data_summary()
    elif args.today:
        today_str = date.today().strftime('%Y-%m-%d')
        test_handler.test_sync_by_date(today_str)
    elif args.date:
        # 验证日期格式
        try:
            datetime.strptime(args.date, '%Y-%m-%d')
            test_handler.test_sync_by_date(args.date)
        except ValueError:
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
    else:
        # 默认显示帮助信息
        parser.print_help()
        print("\n示例用法:")
        print("  python test_data_sync.py --summary                    # 显示数据概览")
        print("  python test_data_sync.py --today                      # 同步今天的数据")
        print("  python test_data_sync.py --date 2025-07-29           # 同步指定日期的数据")

if __name__ == "__main__":
    main()
