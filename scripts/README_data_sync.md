# 数据同步脚本说明

## 概述

这套脚本用于实现从DEBUG数据库到PROD数据库的定时数据同步功能。

## 功能特性

- **定时同步**: 每天早上10:10自动执行数据同步
- **数据过滤**: 只同步当天的数据（根据create_time字段）
- **风险等级过滤**: 只同步risk_type <= 4的数据
- **重复检查**: 避免重复插入已存在的数据
- **日志记录**: 详细的操作日志和统计信息
- **错误处理**: 完善的异常处理和回滚机制

## 文件说明

### 1. daily_data_sync.py
主要的数据同步脚本，包含：
- `DataSyncHandler`: 核心数据同步处理类
- 定时任务调度功能
- 完整的日志记录

### 2. test_data_sync.py
测试脚本，用于：
- 手动测试数据同步功能
- 查看数据概览
- 指定日期进行同步测试

### 3. daily_data_sync.ini
Supervisor配置文件，用于：
- 管理定时任务进程
- 自动重启和日志管理


## 使用方法

### 1. 启动定时任务

使用Supervisor管理：

```bash
# 复制配置文件到supervisor配置目录
sudo cp supervisor_config/daily_data_sync.ini /etc/supervisor/conf.d/

# 重新加载supervisor配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start daily_data_sync

# 查看状态
sudo supervisorctl status daily_data_sync
```

或者直接运行：

```bash
cd /Users/<USER>/Documents/low_sec_detect
python3 scripts/daily_data_sync.py
```

### 2. 测试功能

查看数据概览：
```bash
python3 scripts/test_data_sync.py --summary
```

同步今天的数据：
```bash
python3 scripts/test_data_sync.py --today
```

同步指定日期的数据：
```bash
python3 scripts/test_data_sync.py --date 2025-07-29
```

## 日志文件

- **应用日志**: `logs/daily_data_sync.log`
- **Supervisor日志**: `logs/daily_data_sync_supervisor.log`

## 同步逻辑

1. **数据拉取**: 从DEBUG数据库拉取当天创建的数据
2. **条件过滤**: 只处理risk_type <= 4的记录
3. **重复检查**: 根据host、port、risk_type、url检查是否已存在
4. **数据插入**: 将新数据插入到PROD数据库
5. **统计报告**: 记录插入、跳过、失败的数量

## 监控和维护

### 查看运行状态
```bash
sudo supervisorctl status daily_data_sync
```

### 查看日志
```bash
tail -f logs/daily_data_sync.log
```

### 重启服务
```bash
sudo supervisorctl restart daily_data_sync
```

### 停止服务
```bash
sudo supervisorctl stop daily_data_sync
```

## 注意事项

1. **数据库权限**: 确保脚本有读取DEBUG数据库和写入PROD数据库的权限
2. **网络连接**: 确保服务器能够访问两个数据库
3. **磁盘空间**: 定期清理日志文件，避免磁盘空间不足
4. **时区设置**: 确保服务器时区设置正确，影响定时任务执行时间
5. **数据备份**: 建议在同步前对PROD数据库进行备份

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务状态

2. **权限不足**
   - 检查数据库用户权限
   - 确认文件系统权限

3. **定时任务未执行**
   - 检查Supervisor服务状态
   - 查看系统时间和时区设置
   - 检查脚本路径和Python环境

4. **数据同步异常**
   - 查看详细日志信息
   - 检查数据库表结构是否一致
   - 验证数据格式和约束条件

