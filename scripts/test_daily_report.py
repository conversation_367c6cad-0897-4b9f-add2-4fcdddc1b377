#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试每日扫描结果报告功能
用于验证数据库连接和报告生成是否正常
"""

import os
import sys

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scan_tools.scan_utils.wechat_bot import DailyReportGenerator, WeChatBot

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接...")
    
    generator = DailyReportGenerator()
    
    try:
        # 测试测试数据库连接
        print("\n1. 测试测试数据库连接...")
        debug_conn = generator.get_connection(generator.debug_db_config)
        print("✓ 测试数据库连接成功")
        debug_conn.close()
        
        # 测试正式数据库连接
        print("\n2. 测试正式数据库连接...")
        prod_conn = generator.get_connection(generator.prod_db_config)
        print("✓ 正式数据库连接成功")
        prod_conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_statistics_query():
    """测试统计查询"""
    print("\n" + "=" * 50)
    print("测试统计查询...")
    
    generator = DailyReportGenerator()
    
    try:
        # 测试测试数据库统计
        print("\n1. 获取测试数据库统计...")
        debug_stats = generator.get_risk_statistics(
            generator.debug_db_config, 
            generator.debug_table_name
        )
        print(f"✓ 测试数据库统计获取成功:")
        print(f"  - 总风险数量: {debug_stats['total_count']}")
        print(f"  - 今日新增: {debug_stats['today_count']}")
        print(f"  - 总漏洞类型数: {len(debug_stats['total_top_risks'])}")
        print(f"  - 今日新增漏洞类型数: {len(debug_stats['today_top_risks'])}")
        
        # 测试正式数据库统计
        print("\n2. 获取正式数据库统计...")
        prod_stats = generator.get_risk_statistics(
            generator.prod_db_config, 
            generator.prod_table_name
        )
        print(f"✓ 正式数据库统计获取成功:")
        print(f"  - 总风险数量: {prod_stats['total_count']}")
        print(f"  - 今日新增: {prod_stats['today_count']}")
        print(f"  - 总漏洞类型数: {len(prod_stats['total_top_risks'])}")
        print(f"  - 今日新增漏洞类型数: {len(prod_stats['today_top_risks'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ 统计查询失败: {e}")
        return False

def test_report_generation():
    """测试报告生成"""
    print("\n" + "=" * 50)
    print("测试报告生成...")
    
    generator = DailyReportGenerator()
    
    try:
        report_content = generator.generate_daily_report()
        print("✓ 报告生成成功")
        print("\n报告内容预览:")
        print("-" * 30)
        # 只显示前500个字符
        preview = report_content[:500] + "..." if len(report_content) > 500 else report_content
        print(preview)
        print("-" * 30)
        
        return True, report_content
        
    except Exception as e:
        print(f"✗ 报告生成失败: {e}")
        return False, None

def test_wechat_bot(report_content=None):
    """测试企业微信机器人发送（可选）"""
    print("\n" + "=" * 50)
    print("测试企业微信机器人发送...")
    
    # 这里使用测试webhook地址，实际使用时需要替换
    test_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"
    
    response = input("是否要发送测试消息到企业微信？(y/N): ")
    if response.lower() != 'y':
        print("跳过企业微信发送测试")
        return True
    
    try:
        bot = WeChatBot(test_webhook_url)
        
        if report_content:
            # 发送完整报告
            success = bot.send_markdown(report_content)
        else:
            # 发送测试消息
            success = bot.send_text("📊 每日报告功能测试消息")
        
        if success:
            print("✓ 企业微信消息发送成功")
            return True
        else:
            print("✗ 企业微信消息发送失败")
            return False
            
    except Exception as e:
        print(f"✗ 企业微信发送测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试每日扫描结果报告功能")
    
    # 测试数据库连接
    if not test_database_connection():
        print("\n❌ 数据库连接测试失败，请检查配置")
        return
    
    # 测试统计查询
    if not test_statistics_query():
        print("\n❌ 统计查询测试失败，请检查数据库表结构")
        return
    
    # 测试报告生成
    success, report_content = test_report_generation()
    if not success:
        print("\n❌ 报告生成测试失败")
        return
    
    # 测试企业微信发送
    test_wechat_bot(report_content)
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成！")

if __name__ == "__main__":
    main()
