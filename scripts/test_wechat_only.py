#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试企业微信机器人功能（不依赖数据库）
"""

import os
import sys

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scan_tools.scan_utils.wechat_bot import WeChatBot

def test_wechat_bot():
    """测试企业微信机器人基本功能"""
    print("测试企业微信机器人功能...")
    
    # 企业微信机器人webhook地址
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"
    
    try:
        bot = WeChatBot(webhook_url)
        
        # 测试发送文本消息
        print("1. 测试发送文本消息...")
        success = bot.send_text("📊 每日扫描结果报告功能测试消息")
        if success:
            print("✓ 文本消息发送成功")
        else:
            print("✗ 文本消息发送失败")
        
        # 测试发送markdown消息
        print("\n2. 测试发送markdown消息...")
        test_markdown = """📊 **每日安全扫描结果报告测试**

**扫描目标**: 228w ip资产
**扫描时间**: 2025-07-31 10:00:00

---

## 📈 风险数据统计

### 🧪 测试数据库
**总风险数量**: 1,234 个
**今日新增**: 56 个

### 🏭 正式数据库  
**总风险数量**: 987 个
**今日新增**: 23 个

---

## 🔍 漏洞类型排行

### 📊 测试数据库总漏洞类型排行 (TOP4)
1. **redis未授权漏洞**: 456 个
2. **文件服务暴露漏洞**: 234 个
3. **ElasticSearch未授权访问**: 123 个
4. **ClickHouse未授权访问**: 89 个

---

⚠️ 这是一条测试消息，请忽略！"""

        success = bot.send_markdown(test_markdown)
        if success:
            print("✓ Markdown消息发送成功")
        else:
            print("✗ Markdown消息发送失败")
            
        return True
        
    except Exception as e:
        print(f"✗ 企业微信机器人测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试企业微信机器人功能")
    print("=" * 50)
    
    response = input("是否要发送测试消息到企业微信？(y/N): ")
    if response.lower() != 'y':
        print("跳过企业微信发送测试")
        return
    
    if test_wechat_bot():
        print("\n✅ 企业微信机器人功能测试完成！")
    else:
        print("\n❌ 企业微信机器人功能测试失败！")

if __name__ == "__main__":
    main()
