#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版每日扫描结果报告发送脚本
功能：
1. 从测试数据库和正式数据库获取风险统计数据
2. 生成每日扫描结果报告
3. 通过企业微信机器人发送报告
"""

import os
import sys
import logging
from datetime import datetime

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scan_tools.scan_utils.wechat_bot import send_daily_scan_report

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 企业微信机器人webhook地址
WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"

def send_daily_report(use_mock_data=False):
    """发送每日扫描结果报告"""
    try:
        if use_mock_data:
            logger.info("开始生成并发送每日扫描结果报告（使用模拟数据）...")
        else:
            logger.info("开始生成并发送每日扫描结果报告...")
        
        # 发送报告
        success = send_daily_scan_report(WEBHOOK_URL, use_mock_data=use_mock_data)
        
        if success:
            logger.info("每日扫描结果报告发送成功")
            return True
        else:
            logger.error("每日扫描结果报告发送失败")
            return False
            
    except Exception as e:
        logger.error(f"发送每日报告时发生异常: {str(e)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版每日扫描结果报告发送脚本')
    parser.add_argument('--mock', action='store_true', help='使用模拟数据（用于测试）')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("每日扫描结果报告发送脚本")
    print("=" * 60)
    
    if args.mock:
        print("使用模拟数据模式")
    else:
        print("使用真实数据模式")
    
    success = send_daily_report(use_mock_data=args.mock)
    
    if success:
        print("\n✅ 报告发送完成！")
    else:
        print("\n❌ 报告发送失败！")
        sys.exit(1)
