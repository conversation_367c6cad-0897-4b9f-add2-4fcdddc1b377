# 每日扫描结果报告功能使用说明

## 功能概述

每日扫描结果报告功能可以自动从测试数据库和正式数据库获取风险统计数据，生成详细的每日报告，并通过企业微信机器人推送给相关人员。

## 报告内容

每日报告包含以下信息：

### 基本信息
- **扫描目标**: 228w ip资产
- **扫描时间**: 每天早上10:00:00

### 风险数据统计
- 测试数据库和正式数据库的总风险数量
- 测试数据库和正式数据库的今日新增风险数量

### 漏洞类型排行
- 测试数据库总漏洞类型排行前4名（根据risk_name分组）
- 正式数据库总漏洞类型排行前4名（根据risk_name分组）
- 今日新增漏洞类型排行前4名（若数量为0则不显示）

## 文件结构

```
scan_tools/scan_utils/wechat_bot.py    # 核心功能实现
scripts/daily_report_sender.py         # 每日报告发送脚本
scripts/test_daily_report.py          # 测试脚本
docs/daily_report_usage.md            # 使用说明文档
```

## 使用方法

### 1. 立即发送报告

```bash
# 方法1：直接运行发送脚本
cd /path/to/low_sec_detect
python scripts/daily_report_sender.py --now

# 方法2：测试脚本（包含详细测试过程）
python scripts/test_daily_report.py
```

### 2. 启动定时任务

```bash
# 启动定时任务（每天10:05执行）
python scripts/daily_report_sender.py --schedule
```

### 3. 在代码中调用

```python
from scan_tools.scan_utils.wechat_bot import send_daily_scan_report

# 企业微信机器人webhook地址
webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key"

# 发送每日报告
success = send_daily_scan_report(webhook_url)
```

## 配置说明

### 数据库配置

脚本中已配置了测试数据库和正式数据库的连接信息：

**测试数据库**:
- 主机: *************
- 数据库: web_scan
- 表名: dir_list_vul_all

**正式数据库**:
- 主机: *************
- 数据库: hunyuan_sec_data
- 表名: dir_list_vul

### 企业微信配置

需要在以下文件中配置正确的webhook地址：
- `scripts/daily_report_sender.py`
- `scan_tools/scan_utils/wechat_bot.py`

## 定时任务设置

### 使用crontab

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天10:05执行）
5 10 * * * cd /path/to/low_sec_detect && python scripts/daily_report_sender.py --now
```

### 使用systemd

创建服务文件 `/etc/systemd/system/daily-report.service`:

```ini
[Unit]
Description=Daily Security Scan Report
After=network.target

[Service]
Type=oneshot
User=your-user
WorkingDirectory=/path/to/low_sec_detect
ExecStart=/usr/bin/python3 scripts/daily_report_sender.py --now

[Install]
WantedBy=multi-user.target
```

创建定时器文件 `/etc/systemd/system/daily-report.timer`:

```ini
[Unit]
Description=Daily Security Scan Report Timer
Requires=daily-report.service

[Timer]
OnCalendar=*-*-* 10:05:00
Persistent=true

[Install]
WantedBy=timers.target
```

启用定时器：
```bash
sudo systemctl enable daily-report.timer
sudo systemctl start daily-report.timer
```

## 测试和调试

### 运行测试脚本

```bash
python scripts/test_daily_report.py
```

测试脚本会依次验证：
1. 数据库连接
2. 统计查询
3. 报告生成
4. 企业微信发送（可选）

### 查看日志

日志文件位置：`logs/daily_report.log`

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务是否正常

2. **企业微信发送失败**
   - 检查webhook地址是否正确
   - 验证网络连接
   - 确认机器人是否被禁用

3. **数据查询异常**
   - 检查表名是否正确
   - 验证字段名是否存在
   - 确认数据库权限

## 报告示例

```markdown
📊 **每日安全扫描结果报告**

**扫描目标**: 228w ip资产
**扫描时间**: 2025-07-31 10:00:00

---

## 📈 风险数据统计

### 🧪 测试数据库
**总风险数量**: 1,234 个
**今日新增**: 56 个

### 🏭 正式数据库  
**总风险数量**: 987 个
**今日新增**: 23 个

---

## 🔍 漏洞类型排行

### 📊 测试数据库总漏洞类型排行 (TOP4)
1. **redis未授权漏洞**: 456 个
2. **文件服务暴露漏洞**: 234 个
3. **ElasticSearch未授权访问**: 123 个
4. **ClickHouse未授权访问**: 89 个

### 📊 正式数据库总漏洞类型排行 (TOP4)
1. **redis未授权漏洞**: 345 个
2. **文件服务暴露漏洞**: 198 个
3. **ElasticSearch未授权访问**: 87 个
4. **ClickHouse未授权访问**: 65 个

---

## 🆕 今日新增漏洞类型排行 (TOP4)

### 🧪 测试数据库今日新增
1. **redis未授权漏洞**: 23 个
2. **文件服务暴露漏洞**: 12 个

### 🏭 正式数据库今日新增
1. **redis未授权漏洞**: 15 个
2. **文件服务暴露漏洞**: 8 个

---

⚠️ 请及时关注和处理相关安全风险！
```
