# core/utils.py
import os
import csv
import chardet
import shutil
from typing import List, Dict

def load_ip_owner_info(filepath: str) -> Dict[str, dict]:
    """
    从CSV文件中加载IP与负责人的映射关系。
    CSV文件应包含以下列: ip, user_name, bg, dept, center, oa_group
    """
    owner_info = {}
    if not os.path.exists(filepath):
        print(f"警告: IP负责人信息文件未找到: {filepath}")
        return owner_info
        
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            reader = csv.DictReader(f)
            for row in reader:
                ip = row.get('ip')
                if ip:
                    owner_info[ip] = {
                        "user_name": row.get('user_name', ''),
                        "bg": row.get('bg', ''),
                        "dept": row.get('dept', ''),
                        "center": row.get('center', ''),
                        "oa_group": row.get('oa_group', '')
                    }
    except Exception as e:
        print(f"错误: 加载IP负责人信息时失败。原因: {e}")
        
    print(f"信息: 成功从 '{filepath}' 加载 {len(owner_info)} 条IP负责人信息。")
    return owner_info

def split_file_into_chunks(filepath: str, chunk_size: int, temp_dir: str) -> List[str]:
    """
    将一个大文本文件按指定的行数分割成多个小文件（分片）。
    """
    if not os.path.exists(filepath):
        print(f"错误: 输入文件不存在: {filepath}")
        return []
        
    # 修正逻辑：先清理旧目录，再创建新目录
    if os.path.exists(temp_dir):
        cleanup_temp_chunks(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)

    chunk_files = []
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            file_count = 0
            lines = []
            for line in f:
                lines.append(line)
                if len(lines) >= chunk_size:
                    file_count += 1
                    chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                    with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                        chunk_file.writelines(lines)
                    chunk_files.append(chunk_filename)
                    lines = []
            
            if lines:
                file_count += 1
                chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                    chunk_file.writelines(lines)
                chunk_files.append(chunk_filename)

        print(f"信息: 已成功将文件 '{os.path.basename(filepath)}' 分割成 {len(chunk_files)} 个分片。")
        return chunk_files
    except Exception as e:
        print(f"错误: 文件分割过程中失败。原因: {e}")
        return []

def cleanup_temp_chunks(temp_dir: str):
    """清理临时分片目录下的所有文件。"""
    if os.path.exists(temp_dir):
        try:
            shutil.rmtree(temp_dir)
            print(f"信息: 已成功清理临时分片目录: {temp_dir}")
        except OSError as e:
            print(f"错误: 清理临时目录 {temp_dir} 失败。原因: {e}")

def merge_chunk_logs(chunk_log_files: List[str], output_log_path: str) -> bool:
    """
    将多个切片日志文件合并成一个总日志文件。

    :param chunk_log_files: 切片日志文件路径列表
    :param output_log_path: 输出的合并日志文件路径
    :return: 合并是否成功
    """
    if not chunk_log_files:
        print("警告: 没有提供任何切片日志文件用于合并。")
        return False

    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_log_path)
        os.makedirs(output_dir, exist_ok=True)

        print(f"信息: 开始合并 {len(chunk_log_files)} 个切片日志文件...")

        with open(output_log_path, 'w', encoding='utf-8') as output_file:
            for i, chunk_log_file in enumerate(chunk_log_files):
                if not os.path.exists(chunk_log_file):
                    print(f"警告: 切片日志文件不存在，跳过: {chunk_log_file}")
                    continue

                print(f" - 正在合并切片 {i+1}/{len(chunk_log_files)}: {os.path.basename(chunk_log_file)}")

                # 添加切片分隔符
                output_file.write(f"\n# ===== 切片 {i+1} 开始: {os.path.basename(chunk_log_file)} =====\n")

                try:
                    with open(chunk_log_file, 'r', encoding='utf-8', errors='ignore') as chunk_file:
                        content = chunk_file.read()
                        if content.strip():  # 只写入非空内容
                            output_file.write(content)
                            if not content.endswith('\n'):
                                output_file.write('\n')
                        else:
                            output_file.write("# 此切片无扫描结果\n")
                except Exception as e:
                    print(f"警告: 读取切片日志文件 {chunk_log_file} 失败: {e}")
                    output_file.write(f"# 读取此切片失败: {e}\n")

                output_file.write(f"# ===== 切片 {i+1} 结束 =====\n\n")

        print(f"成功: 已将所有切片日志合并到: {output_log_path}")
        return True

    except Exception as e:
        print(f"错误: 合并切片日志文件失败: {e}")
        return False
