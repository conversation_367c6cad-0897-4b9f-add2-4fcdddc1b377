#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Set, Optional
from datetime import datetime

from .config import settings
from .core import scanner, utils
from .database.models import Vulnerability

from scan_tools.scan_model.db_handler import DatabaseHandler

def run_initial_scan():
    """
    任务一: 执行原始扫描
    对大规模的原始IP列表文件进行分片、扫描，并将发现的漏洞存入数据库。
    """
    print("\n" + "="*20 + " 任务一: 开始执行目录泄露原始扫描 " + "="*20)
    
    # 1. 准备工作：检查原始目标文件、加载IP信息、初始化数据库
    if not os.path.exists(settings.RAW_TARGETS_FILE):
        print(f"致命错误: 原始扫描目标文件未找到: {settings.RAW_TARGETS_FILE}")
        return
        
    print("信息: 正在加载IP负责人数据...")
    ip_owner_info = utils.load_ip_owner_info(settings.RAW_CSV_FILE)
    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 2. 对大文件进行分片
    print("信息: 正在将大型IP列表文件分片...")
    chunk_files = utils.split_file_into_chunks(
        filepath=settings.RAW_TARGETS_FILE,
        chunk_size=settings.IP_CHUNK_SIZE,
        temp_dir=settings.TEMP_CHUNK_DIR
    )
    if not chunk_files:
        print("错误: IP文件分片失败，中止原始扫描。")
        return

    # 3. 创建带时间戳的切片日志目录
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chunk_log_dir_for_run = os.path.join(settings.CHUNK_LOG_BASE_DIR, f"run_{run_timestamp}")
    os.makedirs(chunk_log_dir_for_run, exist_ok=True)
    print(f"信息: 本次运行的切片日志将保存在: {chunk_log_dir_for_run}")

    # 4. 遍历分片进行扫描
    all_live_vulns_set: Set[str] = set()
    chunk_log_files = []  # 保存所有切片日志文件路径
    total_chunks = len(chunk_files)

    for i, chunk_file in enumerate(chunk_files):
        try:
            print(f"\n--- 正在处理分片 {i + 1}/{total_chunks}: {os.path.basename(chunk_file)} ---")

            # 为每个分片指定一个持久化的日志文件
            chunk_log_filename = f"chunk_{i + 1}_scan.log"
            chunk_log_path = os.path.join(chunk_log_dir_for_run, chunk_log_filename)
            chunk_log_files.append(chunk_log_path)

            scan_success = scanner.run_txportmap(
                target_file_path=chunk_file,
                ports=settings.TXPORTMAP_PORTS,
                log_file_path=chunk_log_path,
                txportmap_executable=settings.TXPORTMAP_EXECUTABLE
            )

            if scan_success:
                live_vulns_in_chunk = scanner.parse_txportmap_log(chunk_log_path)
                all_live_vulns_set.update(live_vulns_in_chunk)
                print(f"信息: 切片 {i + 1} 扫描完成，发现 {len(live_vulns_in_chunk)} 个漏洞，日志已保存到: {chunk_log_filename}")
            else:
                print(f"警告: 分片 {os.path.basename(chunk_file)} 扫描失败，跳过此分片。")
        except Exception as err:
            print(f"错误: 处理分片 {os.path.basename(chunk_file)} 时发生异常: {err}")

    # 5. 合并所有切片日志
    merged_log_filename = f"txportmap_merged_scan_{run_timestamp}.log"
    merged_log_path = os.path.join(settings.LOGS_DIR, merged_log_filename)

    if chunk_log_files:
        print(f"\n信息: 开始合并所有切片日志...")
        merge_success = utils.merge_chunk_logs(chunk_log_files, merged_log_path)
        if merge_success:
            print(f"成功: 已将所有切片日志合并到: {merged_log_filename}")
        else:
            print("警告: 切片日志合并失败")

    utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR) # 清理分片文件，但保留切片日志
    print(f"\n信息: 所有分片扫描完成，共发现 {len(all_live_vulns_set)} 个疑似漏洞。")
    print(f"信息: 切片日志已保存在: {chunk_log_dir_for_run}")
    print(f"信息: 合并日志已保存在: {merged_log_path}")

    # 6. 将所有发现的漏洞写入数据库
    if all_live_vulns_set:
        print("\n信息: 开始将所有扫描结果统一写入数据库...")
        for host_port in all_live_vulns_set:
            try:
                host, port_str = host_port.split(':')
                port = int(port_str)
                # 构造URL并创建Vulnerability对象
                url = f"http://{host}:{port}"
                vuln = Vulnerability(host=host, port=port, url=url)
                owner = ip_owner_info.get(host, {})
                db_handler.add_vulnerability(vuln, owner)
            except Exception as e:
                print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")

    # 7. （可选，但不推荐）标记不再存在的漏洞为已修复
    # print("\n信息: 正在更新已修复漏洞的状态...")
    # db_handler.mark_fixed_vulnerabilities(all_live_vulns_set)
    
    print("\n" + "="*20 + " 任务一: 目录泄露原始扫描完成 " + "="*20)


def run_audit_scan():
    """
    任务二: 执行复测扫描
    从数据库拉取活跃的目录泄露漏洞，再次扫描以确认其状态。
    """
    print("\n" + "="*20 + " 任务二: 开始执行目录泄露复测扫描 " + "="*20)
    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 1. 拉取待复测目标
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_DIRLIST
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的目录泄露活跃漏洞，任务结束。")
        return

    # 2. 将目标写入临时文件
    initial_targets_set: Set[str] = set()
    with open(settings.AUDIT_TARGETS_FILE, 'w') as f:
        for item in targets_to_retest:
            host_port_str = f"{item['host']}:{item['port']}"
            f.write(f"{item['host']}\n") # TxPortMap 只需 host
            initial_targets_set.add(host_port_str)
    print(f"信息: 已将 {len(initial_targets_set)} 个唯一目标写入复测文件: {settings.AUDIT_TARGETS_FILE}")
    
    # 3. 执行复测扫描
    scan_success = scanner.run_txportmap(
        target_file_path=settings.AUDIT_TARGETS_FILE,
        ports=settings.TXPORTMAP_PORTS,
        log_file_path=settings.AUDIT_SCAN_LOG_FILE,
        txportmap_executable=settings.TXPORTMAP_EXECUTABLE
    )

    if not scan_success:
        print("警告: 复测扫描执行失败，将中止本次复测流程。")
        return
        
    # 4. 解析结果并更新数据库
    live_vulns_in_audit = scanner.parse_txportmap_log(settings.AUDIT_SCAN_LOG_FILE)
    print(f"信息: 复测扫描发现 {len(live_vulns_in_audit)} 个仍然存活的漏洞。")

    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个漏洞已被修复。")
    
    db_handler.batch_update_status_to_fixed(
        fixed_host_ports=list(fixed_vulns_set),
        risk_type=settings.RISK_TYPE_DIRLIST
    )
    
    # 5. 清理临时文件
    os.remove(settings.AUDIT_TARGETS_FILE)
    if os.path.exists(settings.AUDIT_SCAN_LOG_FILE):
        os.remove(settings.AUDIT_SCAN_LOG_FILE)
    print("信息: 已清理临时复测文件和日志。")

    print("\n" + "="*20 + " 任务二: 目录泄露复测扫描完成 " + "="*20)


def process_latest_initial_report(specific_log_path: Optional[str] = None):
    """
    功能三: 处理指定的或最新的原始扫描日志，并添加新漏洞。
    """
    print("\n" + "="*20 + " 功能三: 处理目录泄露原始报告 " + "="*20)

    log_to_process = specific_log_path
    if not log_to_process:
        print("信息: 未指定特定日志，将自动查找最新日志进行处理...")
        log_to_process = find_latest_log(settings.LOGS_DIR, "initial")

    if not log_to_process or not os.path.exists(log_to_process):
        print("错误: 在日志目录中未找到任何可处理的原始扫描日志。")
        return

    print(f"信息: 正在处理日志文件: {os.path.basename(log_to_process)}")

    # 初始化数据库和IP信息
    db_handler = DatabaseHandler(settings.DB_CONFIG)
    ip_owner_info = utils.load_ip_owner_info(settings.RAW_CSV_FILE)

    # 解析日志文件
    live_vulns_set = scanner.parse_txportmap_log(log_to_process)

    if not live_vulns_set:
        print(f"信息: 日志 {os.path.basename(log_to_process)} 中未发现任何目录泄露漏洞。")
        return

    print(f"信息: 从日志中解析出 {len(live_vulns_set)} 个疑似漏洞，开始写入数据库...")

    # 将漏洞写入数据库
    added_count = 0
    for host_port in live_vulns_set:
        try:
            host, port_str = host_port.split(':')
            port = int(port_str)
            # 构造URL并创建Vulnerability对象
            url = f"http://{host}:{port}"
            vuln = Vulnerability(host=host, port=port, url=url)
            owner = ip_owner_info.get(host, {})
            db_handler.add_vulnerability(vuln, owner)
            added_count += 1
        except Exception as e:
            print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")

    print(f"信息: 成功处理 {added_count} 个漏洞并写入数据库。")
    print("\n" + "="*20 + " 功能三: 处理完成 " + "="*20)


def process_latest_audit_report(specific_log_path: Optional[str] = None):
    """
    功能四: 处理指定的或最新的复测扫描日志，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 功能四: 处理目录泄露复测报告 " + "="*20)

    log_to_process = specific_log_path
    if not log_to_process:
        print("信息: 未指定特定日志，将自动查找最新日志进行处理...")
        log_to_process = find_latest_log(settings.LOGS_DIR, "audit")

    if not log_to_process or not os.path.exists(log_to_process):
        print("错误: 在日志目录中未找到任何可处理的复测日志。")
        return

    print(f"信息: 正在处理复测日志文件: {os.path.basename(log_to_process)}")

    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 1. 获取所有待复测的目标，用于比对
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_DIRLIST
    )
    initial_targets_set = {f"{item['host']}:{item['port']}" for item in targets_to_retest}
    print(f"信息: 数据库中有 {len(initial_targets_set)} 个待复测的目录泄露漏洞。")

    # 2. 从复测日志中解析出仍然存活的漏洞
    live_vulns_in_audit = scanner.parse_txportmap_log(log_to_process)
    print(f"信息: 复测扫描发现 {len(live_vulns_in_audit)} 个仍然存活的漏洞。")

    # 3. 计算出已修复的漏洞（在待复测列表但不在存活列表）
    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个漏洞已被修复。")

    # 4. 更新数据库
    if fixed_vulns_set:
        db_handler.batch_update_status_to_fixed(
            fixed_host_ports=list(fixed_vulns_set),
            risk_type=settings.RISK_TYPE_DIRLIST
        )
        print(f"信息: 已将 {len(fixed_vulns_set)} 个漏洞状态更新为已修复。")
    else:
        print("信息: 没有发现已修复的漏洞。")

    print("\n" + "="*20 + " 功能四: 处理完成 " + "="*20)


# --- 辅助函数 ---

def find_latest_log(log_dir: str, log_type: str) -> Optional[str]:
    """
    在指定目录中查找最新的日志文件。
    log_type: "initial" 或 "audit"
    """
    if log_type == "initial":
        # 查找原始扫描日志：txportmap_merged_scan_*.log 或 txportmap_initial_scan_*.log
        prefixes = ["txportmap_merged_scan_", "txportmap_initial_scan_"]
    elif log_type == "audit":
        # 查找复测日志：txportmap_audit_scan_*.log
        prefixes = ["txportmap_audit_scan_"]
    else:
        print(f"错误: 不支持的日志类型: {log_type}")
        return None

    if not os.path.isdir(log_dir):
        print(f"错误: 日志目录不存在: {log_dir}")
        return None

    # 查找匹配的日志文件
    matching_files = []
    for filename in os.listdir(log_dir):
        if filename.endswith('.log'):
            for prefix in prefixes:
                if filename.startswith(prefix):
                    matching_files.append(os.path.join(log_dir, filename))
                    break

    if not matching_files:
        print(f"警告: 在目录 {log_dir} 中未找到 {log_type} 类型的日志文件。")
        return None

    # 返回最新的文件（按修改时间）
    latest_file = max(matching_files, key=os.path.getctime)
    print(f"信息: 找到最新的 {log_type} 日志: {os.path.basename(latest_file)}")
    return latest_file
