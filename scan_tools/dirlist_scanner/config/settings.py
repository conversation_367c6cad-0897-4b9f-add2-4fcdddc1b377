# config/settings.py
from datetime import datetime
# 导入所有全局共享配置
from config.common_config import *

# ==============================================================================
# 基础配置 (项目特有)
# ==============================================================================
# 项目根目录
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# 工作目录
WORKSPACE_DIR = os.path.join(BASE_DIR, "assets")
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

# ==============================================================================
# 漏洞与扫描配置 (项目特有)
# ==============================================================================
# 漏洞类型定义
RISK_TYPE_DIRLIST = 1
DIRLIST_VULN_DETAILS = {
    'risk_id': RISK_TYPE_DIRLIST,
    'risk_type': '目录遍历/目录泄露',
    'risk_level': '1',
    'risk_source': 'txportmap',
}

# TxPortMap (目录泄露扫描器) 相关配置
TXPORTMAP_EXECUTABLE = os.path.join(BASE_DIR, "TxPortMap")
TXPORTMAP_PORTS = "80,81,8080,443"

# 原始扫描相关配置
RAW_TARGETS_FILE = SELECT_IP_FILE  # 使用从common_config导入的路径
RAW_CSV_FILE = IP_INFO_CSV_PATH
INITIAL_SCAN_LOG_FILE = os.path.join(LOGS_DIR, f"txportmap_initial_scan_{datetime.now().strftime('%Y%m%d')}.log")
IP_CHUNK_SIZE = 100000  # 原始IP文件分片大小
TEMP_CHUNK_DIR = os.path.join(BASE_DIR, 'temp_chunks')

# 切片日志相关配置 (参考其他扫描器实现)
CHUNK_LOG_BASE_DIR = os.path.join(BASE_DIR, 'chunk_logs')  # 切片日志基础目录
os.makedirs(CHUNK_LOG_BASE_DIR, exist_ok=True)

# 复测扫描相关配置
AUDIT_TARGETS_FILE = os.path.join(WORKSPACE_DIR, f"dirlist_audit_targets_{datetime.now().strftime('%Y%m%d')}.txt")
AUDIT_SCAN_LOG_FILE = os.path.join(LOGS_DIR, f"txportmap_audit_scan_{datetime.now().strftime('%Y%m%d')}.log")

# ==============================================================================
# 定时任务配置 (项目特有)
# ==============================================================================
# 每日执行扫描任务的时间 (24小时制)


# --- 漏洞信息配置 ---
VULN_CREATOR = "oldbuddyxin"
