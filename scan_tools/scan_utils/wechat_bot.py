#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信机器人消息发送模块
用于向企业微信群聊发送消息通知
"""

import json
import requests
import logging
from typing import Optional, Dict, Any
from datetime import datetime


class WeChatBot:
    """企业微信机器人类"""
    
    def __init__(self, webhook_url: str):
        """
        初始化企业微信机器人
        
        Args:
            webhook_url: 企业微信机器人的webhook地址
        """
        self.webhook_url = webhook_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'WeChatBot/1.0'
        })
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def send_text(self, content: str, mentioned_list: Optional[list] = None, 
                  mentioned_mobile_list: Optional[list] = None) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_list: @的用户userid列表
            mentioned_mobile_list: @的用户手机号列表
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 添加@用户
        if mentioned_list or mentioned_mobile_list:
            data["text"]["mentioned_list"] = mentioned_list or []
            data["text"]["mentioned_mobile_list"] = mentioned_mobile_list or []
        
        return self._send_message(data)
    
    def send_markdown(self, content: str) -> bool:
        """
        发送markdown消息
        
        Args:
            content: markdown格式的消息内容
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        return self._send_message(data)
    
    def send_image(self, base64_data: str, md5_hash: str) -> bool:
        """
        发送图片消息
        
        Args:
            base64_data: 图片的base64编码数据
            md5_hash: 图片的md5值
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "image",
            "image": {
                "base64": base64_data,
                "md5": md5_hash
            }
        }
        
        return self._send_message(data)
    
    def send_news(self, articles: list) -> bool:
        """
        发送图文消息
        
        Args:
            articles: 图文消息列表，每个元素包含title, description, url, picurl
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
        
        return self._send_message(data)
    
    def send_file(self, media_id: str) -> bool:
        """
        发送文件消息
        
        Args:
            media_id: 文件的media_id，通过上传多媒体文件接口获取
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "file",
            "file": {
                "media_id": media_id
            }
        }
        
        return self._send_message(data)
    
    def send_template_card(self, card_type: str, card_data: Dict[str, Any]) -> bool:
        """
        发送模板卡片消息
        
        Args:
            card_type: 卡片类型 (text_notice, news_notice等)
            card_data: 卡片数据
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "template_card",
            "template_card": {
                "card_type": card_type,
                **card_data
            }
        }
        
        return self._send_message(data)
    
    def _send_message(self, data: Dict[str, Any]) -> bool:
        """
        发送消息的内部方法
        
        Args:
            data: 消息数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            response = self.session.post(
                self.webhook_url,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"消息发送成功: {result.get('errmsg', 'ok')}")
                    return True
                else:
                    self.logger.error(f"消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"HTTP请求失败: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求异常: {str(e)}")
            return False
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析异常: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"未知异常: {str(e)}")
            return False


def send_scan_result_notification(webhook_url: str, scan_type: str, 
                                target: str, result_count: int, 
                                high_risk_count: int = 0) -> bool:
    """
    发送扫描结果通知
    
    Args:
        webhook_url: 企业微信机器人webhook地址
        scan_type: 扫描类型
        target: 扫描目标
        result_count: 结果总数
        high_risk_count: 高风险数量
        
    Returns:
        bool: 发送是否成功
    """
    bot = WeChatBot(webhook_url)
    
    # 构建消息内容
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    if high_risk_count > 0:
        content = f"""🚨 **安全扫描告警通知**
        
**扫描类型**: {scan_type}
**扫描目标**: {target}
**扫描时间**: {current_time}
**发现问题**: {result_count} 个
**高风险问题**: {high_risk_count} 个

⚠️ 发现高风险安全问题，请及时处理！"""
    else:
        content = f"""✅ **安全扫描完成通知**
        
**扫描类型**: {scan_type}
**扫描目标**: {target}
**扫描时间**: {current_time}
**发现问题**: {result_count} 个

扫描已完成，请查看详细结果。"""
    
    return bot.send_markdown(content)


def send_error_notification(webhook_url: str, error_type: str, 
                          error_message: str, target: str = "") -> bool:
    """
    发送错误通知
    
    Args:
        webhook_url: 企业微信机器人webhook地址
        error_type: 错误类型
        error_message: 错误消息
        target: 相关目标
        
    Returns:
        bool: 发送是否成功
    """
    bot = WeChatBot(webhook_url)
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    content = f"""❌ **系统错误通知**
    
**错误类型**: {error_type}
**错误时间**: {current_time}
**相关目标**: {target}
**错误详情**: {error_message}

请检查系统状态并及时处理。"""
    
    return bot.send_markdown(content)


# 使用示例
if __name__ == "__main__":
    # 企业微信机器人webhook地址（需要替换为实际地址）
    WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"
    
    # 创建机器人实例
    bot = WeChatBot(WEBHOOK_URL)
    
    # 发送文本消息
    bot.send_text("这是一条测试消息")
    
    # 发送markdown消息
    markdown_content = """
    ## 扫描结果报告
    
    **扫描目标**: 192.168.1.1-192.168.1.100
    **扫描时间**: 2024-01-01 12:00:00
    **发现漏洞**: 5个
    
    ### 高危漏洞
    - Redis未授权访问
    - 目录遍历漏洞
    
    ### 中危漏洞  
    - 弱口令
    - 信息泄露
    
    请及时处理相关安全问题。
    """
    bot.send_markdown(markdown_content)
    
    # 发送扫描结果通知
    send_scan_result_notification(
        WEBHOOK_URL, 
        "Redis扫描", 
        "192.168.1.0/24", 
        10, 
        3
    )
    
    # 发送错误通知
    send_error_notification(
        WEBHOOK_URL,
        "连接超时",
        "无法连接到目标服务器 192.168.1.100:6379",
        "192.168.1.100"
    )