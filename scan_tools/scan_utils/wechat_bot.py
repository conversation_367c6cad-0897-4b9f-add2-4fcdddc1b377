#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信机器人消息发送模块
用于向企业微信群聊发送消息通知
"""

import json
import requests
import logging
import pymysql
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime


class WeChatBot:
    """企业微信机器人类"""
    
    def __init__(self, webhook_url: str):
        """
        初始化企业微信机器人
        
        Args:
            webhook_url: 企业微信机器人的webhook地址
        """
        self.webhook_url = webhook_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'WeChatBot/1.0'
        })
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def send_text(self, content: str, mentioned_list: Optional[list] = None, 
                  mentioned_mobile_list: Optional[list] = None) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_list: @的用户userid列表
            mentioned_mobile_list: @的用户手机号列表
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 添加@用户
        if mentioned_list or mentioned_mobile_list:
            data["text"]["mentioned_list"] = mentioned_list or []
            data["text"]["mentioned_mobile_list"] = mentioned_mobile_list or []
        
        return self._send_message(data)
    
    def send_markdown(self, content: str) -> bool:
        """
        发送markdown消息
        
        Args:
            content: markdown格式的消息内容
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        return self._send_message(data)
    
    def send_image(self, base64_data: str, md5_hash: str) -> bool:
        """
        发送图片消息
        
        Args:
            base64_data: 图片的base64编码数据
            md5_hash: 图片的md5值
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "image",
            "image": {
                "base64": base64_data,
                "md5": md5_hash
            }
        }
        
        return self._send_message(data)
    
    def send_news(self, articles: list) -> bool:
        """
        发送图文消息
        
        Args:
            articles: 图文消息列表，每个元素包含title, description, url, picurl
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
        
        return self._send_message(data)
    
    def send_file(self, media_id: str) -> bool:
        """
        发送文件消息
        
        Args:
            media_id: 文件的media_id，通过上传多媒体文件接口获取
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "file",
            "file": {
                "media_id": media_id
            }
        }
        
        return self._send_message(data)
    
    def send_template_card(self, card_type: str, card_data: Dict[str, Any]) -> bool:
        """
        发送模板卡片消息
        
        Args:
            card_type: 卡片类型 (text_notice, news_notice等)
            card_data: 卡片数据
            
        Returns:
            bool: 发送是否成功
        """
        data = {
            "msgtype": "template_card",
            "template_card": {
                "card_type": card_type,
                **card_data
            }
        }
        
        return self._send_message(data)
    
    def _send_message(self, data: Dict[str, Any]) -> bool:
        """
        发送消息的内部方法
        
        Args:
            data: 消息数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            response = self.session.post(
                self.webhook_url,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"消息发送成功: {result.get('errmsg', 'ok')}")
                    return True
                else:
                    self.logger.error(f"消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"HTTP请求失败: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求异常: {str(e)}")
            return False
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析异常: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"未知异常: {str(e)}")
            return False


def send_scan_result_notification(webhook_url: str, scan_type: str, 
                                target: str, result_count: int, 
                                high_risk_count: int = 0) -> bool:
    """
    发送扫描结果通知
    
    Args:
        webhook_url: 企业微信机器人webhook地址
        scan_type: 扫描类型
        target: 扫描目标
        result_count: 结果总数
        high_risk_count: 高风险数量
        
    Returns:
        bool: 发送是否成功
    """
    bot = WeChatBot(webhook_url)
    
    # 构建消息内容
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    if high_risk_count > 0:
        content = f"""🚨 **安全扫描告警通知**
        
**扫描类型**: {scan_type}
**扫描目标**: {target}
**扫描时间**: {current_time}
**发现问题**: {result_count} 个
**高风险问题**: {high_risk_count} 个

⚠️ 发现高风险安全问题，请及时处理！"""
    else:
        content = f"""✅ **安全扫描完成通知**
        
**扫描类型**: {scan_type}
**扫描目标**: {target}
**扫描时间**: {current_time}
**发现问题**: {result_count} 个

扫描已完成，请查看详细结果。"""
    
    return bot.send_markdown(content)


def send_error_notification(webhook_url: str, error_type: str, 
                          error_message: str, target: str = "") -> bool:
    """
    发送错误通知
    
    Args:
        webhook_url: 企业微信机器人webhook地址
        error_type: 错误类型
        error_message: 错误消息
        target: 相关目标
        
    Returns:
        bool: 发送是否成功
    """
    bot = WeChatBot(webhook_url)
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    content = f"""❌ **系统错误通知**
    
**错误类型**: {error_type}
**错误时间**: {current_time}
**相关目标**: {target}
**错误详情**: {error_message}

请检查系统状态并及时处理。"""
    
    return bot.send_markdown(content)


class DailyReportGenerator:
    """每日扫描结果报告生成器"""

    def __init__(self):
        # 测试数据库配置
        self.debug_db_config = {
            'host': '*************',
            'user': 'root',
            'password': 'Tegsec_09040',
            'database': 'web_scan',
            'charset': 'utf8mb4'
        }
        self.debug_table_name = 'dir_list_vul_all'

        # 正式数据库配置
        self.prod_db_config = {
            'host': '*************',
            'user': 'hunyuan_staff',
            'password': 'hunyuan_staff0820',
            'database': 'hunyuan_sec_data',
            'charset': 'utf8mb4'
        }
        self.prod_table_name = 'dir_list_vul'

    def get_connection(self, db_config: dict):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**db_config)
            return connection
        except Exception as e:
            print(f"数据库连接失败: {e}")
            raise

    def get_risk_statistics(self, db_config: dict, table_name: str) -> Dict[str, Any]:
        """获取风险统计数据"""
        connection = self.get_connection(db_config)
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取总风险数量
                cursor.execute(f"SELECT COUNT(*) as total_count FROM {table_name}")
                total_count = cursor.fetchone()['total_count']

                # 获取今日新增风险数量
                cursor.execute(f"""
                    SELECT COUNT(*) as today_count
                    FROM {table_name}
                    WHERE DATE(create_time) = CURDATE()
                """)
                today_count = cursor.fetchone()['today_count']

                # 获取总漏洞类型排行前4名
                cursor.execute(f"""
                    SELECT risk_name, COUNT(*) as count
                    FROM {table_name}
                    GROUP BY risk_name
                    ORDER BY count DESC
                    LIMIT 4
                """)
                total_top_risks = cursor.fetchall()

                # 获取今日新增漏洞类型排行前4名（排除数量为0的）
                cursor.execute(f"""
                    SELECT risk_name, COUNT(*) as count
                    FROM {table_name}
                    WHERE DATE(create_time) = CURDATE()
                    GROUP BY risk_name
                    HAVING count > 0
                    ORDER BY count DESC
                    LIMIT 4
                """)
                today_top_risks = cursor.fetchall()

                return {
                    'total_count': total_count,
                    'today_count': today_count,
                    'total_top_risks': total_top_risks,
                    'today_top_risks': today_top_risks
                }
        finally:
            connection.close()

    def generate_daily_report(self, use_mock_data: bool = False) -> str:
        """生成每日扫描结果报告"""
        try:
            if use_mock_data:
                # 使用模拟数据
                debug_stats = {
                    'total_count': 1234,
                    'today_count': 56,
                    'total_top_risks': [
                        {'risk_name': 'redis未授权漏洞', 'count': 456},
                        {'risk_name': '文件服务暴露漏洞', 'count': 234},
                        {'risk_name': 'ElasticSearch未授权访问', 'count': 123},
                        {'risk_name': 'ClickHouse未授权访问', 'count': 89}
                    ],
                    'today_top_risks': [
                        {'risk_name': 'redis未授权漏洞', 'count': 23},
                        {'risk_name': '文件服务暴露漏洞', 'count': 12}
                    ]
                }

                prod_stats = {
                    'total_count': 987,
                    'today_count': 23,
                    'total_top_risks': [
                        {'risk_name': 'redis未授权漏洞', 'count': 345},
                        {'risk_name': '文件服务暴露漏洞', 'count': 198},
                        {'risk_name': 'ElasticSearch未授权访问', 'count': 87},
                        {'risk_name': 'ClickHouse未授权访问', 'count': 65}
                    ],
                    'today_top_risks': [
                        {'risk_name': 'redis未授权漏洞', 'count': 15},
                        {'risk_name': '文件服务暴露漏洞', 'count': 8}
                    ]
                }
            else:
                # 获取真实数据库统计
                debug_stats = self.get_risk_statistics(self.debug_db_config, self.debug_table_name)

                # 获取正式数据库统计
                prod_stats = self.get_risk_statistics(self.prod_db_config, self.prod_table_name)

            # 获取当前时间，设置为每天早上10点
            current_date = datetime.now().strftime("%Y-%m-%d")
            scan_time = f"{current_date} 09:00:00"

            # 构建报告内容
            report_content = f"""📊 **每日安全扫描结果报告**

**扫描目标**: 228w teg-ams全量资产
**扫描时间**: {scan_time}

---

## 📈 风险数据统计

### 1.正式数据库
**总风险数量**: {prod_stats['total_count']} 个
**今日新增**: {prod_stats['today_count']} 个

### 2.测试数据库
**总风险数量**: {debug_stats['total_count']} 个
**今日新增**: {debug_stats['today_count']} 个


---

## 🔍 漏洞类型排行

### 1.测试数据库总漏洞类型排行 (TOP4)
"""

            # 添加测试数据库总漏洞排行
            for i, risk in enumerate(debug_stats['total_top_risks'], 1):
                report_content += f"{i}. **{risk['risk_name']}**: {risk['count']} 个\n"

            report_content += "\n### 📊 正式数据库总漏洞类型排行 (TOP4)\n"

            # 添加正式数据库总漏洞排行
            for i, risk in enumerate(prod_stats['total_top_risks'], 1):
                report_content += f"{i}. **{risk['risk_name']}**: {risk['count']} 个\n"

            # 添加今日新增漏洞排行（如果有数据）
            if debug_stats['today_top_risks'] or prod_stats['today_top_risks']:
                report_content += "\n---\n\n## 🆕 今日新增漏洞类型排行 (TOP4)\n\n"

                if debug_stats['today_top_risks']:
                    report_content += "### 🧪 测试数据库今日新增\n"
                    for i, risk in enumerate(debug_stats['today_top_risks'], 1):
                        report_content += f"{i}. **{risk['risk_name']}**: {risk['count']} 个\n"
                    report_content += "\n"

                if prod_stats['today_top_risks']:
                    report_content += "### 🏭 正式数据库今日新增\n"
                    for i, risk in enumerate(prod_stats['today_top_risks'], 1):
                        report_content += f"{i}. **{risk['risk_name']}**: {risk['count']} 个\n"

            report_content += "\n---\n\n⚠️ 请及时关注和处理相关安全风险！"

            return report_content

        except Exception as e:
            error_content = f"""❌ **每日报告生成失败**

**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**错误详情**: {str(e)}

请检查数据库连接和配置。"""
            return error_content


def send_daily_scan_report(webhook_url: str, use_mock_data: bool = False) -> bool:
    """
    发送每日扫描结果报告

    Args:
        webhook_url: 企业微信机器人webhook地址
        use_mock_data: 是否使用模拟数据（用于测试）

    Returns:
        bool: 发送是否成功
    """
    bot = WeChatBot(webhook_url)
    report_generator = DailyReportGenerator()

    # 生成报告内容
    report_content = report_generator.generate_daily_report(use_mock_data=use_mock_data)

    # 发送报告
    return bot.send_markdown(report_content)


# 使用示例
if __name__ == "__main__":
    # 企业微信机器人webhook地址（需要替换为实际地址）
    WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bca00a49-e939-4f52-a16d-d94ada631dd5"
    
    # 创建机器人实例
    bot = WeChatBot(WEBHOOK_URL)

    # 发送每日扫描结果报告
    print("\n发送每日扫描结果报告...")
    send_daily_scan_report(WEBHOOK_URL)