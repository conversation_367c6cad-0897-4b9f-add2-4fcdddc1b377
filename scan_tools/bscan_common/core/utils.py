# core/utils.py
import os
import csv
import chardet
import pandas as pd
from typing import Dict, List, Union
import shutil

def merge_xlsx_files(file_list: List[str], output_path: str):
    """
    将多个XLSX文件合并成一个单独的XLSX文件。
    """
    if not file_list:
        print("警告: 没有提供任何文件用于合并。")
        return

    all_dfs = []
    print("信息: 开始合并所有分片扫描结果...")
    for f in file_list:
        try:
            df = pd.read_excel(f)
            all_dfs.append(df)
            print(f" - 已读取 {os.path.basename(f)} ({len(df)}行)")
        except Exception as e:
            print(f"错误: 读取文件 {f} 失败，跳过此文件。原因: {e}")

    if not all_dfs:
        print("错误: 未能从任何分片结果中读取到数据，无法生成最终报告。")
        return

    # 合并所有数据
    final_df = pd.concat(all_dfs, ignore_index=True)

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True)

    # 保存到最终的XLSX文件
    try:
        final_df.to_excel(output_path, index=False)
        print(f"\n成功: 所有分片结果已合并完成，并保存到: {output_path}")
        print(f"总计合并了 {len(final_df)} 条记录。")
    except Exception as e:
        print(f"错误: 保存最终合并的XLSX文件失败。原因: {e}")

def merge_xlsx_sheets_separately(file_list: List[str], output_dir: str, base_filename: str):
    """
    从多个XLSX文件中分别提取Sheet1和Sheet2，并分别合并成两个独立的excel文件。

    Args:
        file_list: 要合并的XLSX文件列表
        output_dir: 输出目录
        base_filename: 基础文件名（不含扩展名），最终会生成 {base_filename}_sheet1.xlsx 和 {base_filename}_sheet2.xlsx

    Returns:
        tuple: (sheet1_output_path, sheet2_output_path) 或 (None, None) 如果失败
    """
    if not file_list:
        print("警告: 没有提供任何文件用于合并。")
        return None, None

    sheet1_dfs = []
    sheet2_dfs = []

    print("信息: 开始分别合并所有分片的Sheet1和Sheet2...")

    for f in file_list:
        try:
            # 检查文件是否存在
            if not os.path.exists(f):
                print(f"警告: 文件不存在，跳过: {f}")
                continue

            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(f)
            available_sheets = excel_file.sheet_names

            print(f" - 正在处理 {os.path.basename(f)}，可用sheets: {available_sheets}")

            # 读取Sheet1
            if 'Sheet1' in available_sheets:
                df_sheet1 = pd.read_excel(f, sheet_name='Sheet1')
                if not df_sheet1.empty:
                    sheet1_dfs.append(df_sheet1)
                    print(f"   * Sheet1: {len(df_sheet1)}行")
                else:
                    print(f"   * Sheet1: 空表")
            else:
                print(f"   * 警告: 未找到Sheet1")

            # 读取Sheet2
            if 'Sheet2' in available_sheets:
                df_sheet2 = pd.read_excel(f, sheet_name='Sheet2')
                if not df_sheet2.empty:
                    sheet2_dfs.append(df_sheet2)
                    print(f"   * Sheet2: {len(df_sheet2)}行")
                else:
                    print(f"   * Sheet2: 空表")
            else:
                print(f"   * 警告: 未找到Sheet2")

        except Exception as e:
            print(f"错误: 读取文件 {f} 失败，跳过此文件。原因: {e}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 合并并保存Sheet1
    sheet1_output_path = None
    if sheet1_dfs:
        try:
            final_sheet1_df = pd.concat(sheet1_dfs, ignore_index=True)
            sheet1_output_path = os.path.join(output_dir, f"{base_filename}_sheet1.xlsx")
            final_sheet1_df.to_excel(sheet1_output_path, index=False)
            print(f"\n成功: Sheet1合并完成，保存到: {sheet1_output_path}")
            print(f"Sheet1总计合并了 {len(final_sheet1_df)} 条记录。")

            # 同时保存CSV格式
            sheet1_csv_path = os.path.join(output_dir, f"{base_filename}_sheet1.csv")
            final_sheet1_df.to_csv(sheet1_csv_path, index=False, encoding='utf-8-sig')
            print(f"Sheet1 CSV格式保存到: {sheet1_csv_path}")

        except Exception as e:
            print(f"错误: 保存Sheet1合并文件失败。原因: {e}")
            sheet1_output_path = None
    else:
        print("警告: 没有找到任何有效的Sheet1数据，跳过Sheet1合并。")

    # 合并并保存Sheet2
    sheet2_output_path = None
    if sheet2_dfs:
        try:
            final_sheet2_df = pd.concat(sheet2_dfs, ignore_index=True)
            sheet2_output_path = os.path.join(output_dir, f"{base_filename}_sheet2.xlsx")
            final_sheet2_df.to_excel(sheet2_output_path, index=False)
            print(f"\n成功: Sheet2合并完成，保存到: {sheet2_output_path}")
            print(f"Sheet2总计合并了 {len(final_sheet2_df)} 条记录。")

            # 同时保存CSV格式
            sheet2_csv_path = os.path.join(output_dir, f"{base_filename}_sheet2.csv")
            final_sheet2_df.to_csv(sheet2_csv_path, index=False, encoding='utf-8-sig')
            print(f"Sheet2 CSV格式保存到: {sheet2_csv_path}")

        except Exception as e:
            print(f"错误: 保存Sheet2合并文件失败。原因: {e}")
            sheet2_output_path = None
    else:
        print("警告: 没有找到任何有效的Sheet2数据，跳过Sheet2合并。")

    return sheet1_output_path, sheet2_output_path

# 此函数可直接从另一个项目中复制
def load_ip_info(csv_path: str) -> Dict[str, dict]:
    """从CSV文件中加载IP负责人信息到字典中。"""
    ip_info = {}
    if not os.path.exists(csv_path):
        print(f"警告: IP信息CSV文件在 {csv_path} 未找到。负责人信息将为空。")
        return ip_info
    try:
        with open(csv_path, 'rb') as f:
            raw_data = f.read(50000)
            encoding_result = chardet.detect(raw_data)
        with open(csv_path, 'r', encoding=encoding_result['encoding'], errors='ignore') as f:
            reader = csv.DictReader(f)
            for row in reader:
                ip = row.get('ip')
                if ip:
                    ip_info[ip] = {
                        'bg': row.get('bg', ''), 'dept': row.get('dept', ''), 'center': row.get('center', ''),
                        "oa_group": row.get('oa_group', ''), "user_name": row.get('user_name', '')
                    }
        print(f"信息: 成功加载 {len(ip_info)} 条IP负责人记录。")
    except Exception as e:
        print(f"错误: 加载IP信息CSV失败。原因: {str(e)}")
    return ip_info

def find_latest_excel_report(folder_path: str) -> Union[str, None]:
    """在一个目录中查找最新修改的 .xlsx 文件。"""
    try:
        xlsx_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.xlsx')]
        if not xlsx_files:
            print(f"错误: 目录 {folder_path} 中没有找到 .xlsx 文件。")
            return None

        full_paths = [os.path.join(folder_path, f) for f in xlsx_files]
        latest_file = max(full_paths, key=os.path.getmtime)
        print(f"信息: 找到待处理的最新报告: {os.path.basename(latest_file)}")
        return latest_file
    except FileNotFoundError:
        print(f"错误: 报告目录 {folder_path} 未找到。")
        return None


def load_finger_config(finger_csv_path: str) -> Dict[str, dict]:
    """
    从finger.csv文件中加载漏洞配置信息。

    :param finger_csv_path: finger.csv文件的路径
    :return: 以Keyword为键的漏洞配置字典
    """
    finger_config = {}
    if not os.path.exists(finger_csv_path):
        print(f"警告: finger.csv文件未找到: {finger_csv_path}")
        return finger_config

    try:
        with open(finger_csv_path, 'r', encoding='utf-8', errors='ignore') as f:
            reader = csv.DictReader(f)
            for row in reader:
                keyword = row.get('Keyword', '').strip()
                if keyword:
                    finger_config[keyword.lower()] = {
                        'leak_type': row.get('leak_type', ''),
                        'risk_name': row.get('risk_name', ''),
                        'risk_desc': row.get('risk_desc', ''),
                        'risk_tag': row.get('risk_tag', ''),
                        'risk_type': row.get('risk_type', ''),
                        'guide_link': row.get('guide_link', '')
                    }

        print(f"信息: 成功加载 {len(finger_config)} 个漏洞配置项。")
        if finger_config:
            print(f"加载的漏洞类型: {', '.join(finger_config.keys())}")
    except Exception as e:
        print(f"错误: 加载finger.csv文件失败。原因: {e}")

    return finger_config

def split_file_into_chunks(filepath: str, chunk_size: int, temp_dir: str) -> List[str]:
    """
    将一个大文本文件按指定的行数分割成多个小文件（分片）。
    """
    if not os.path.exists(filepath):
        print(f"错误: 输入文件不存在: {filepath}")
        return []
        
    if os.path.exists(temp_dir):
        cleanup_temp_chunks(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)

    chunk_files = []
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            file_count = 0
            lines = []
            for line in f:
                lines.append(line)
                if len(lines) >= chunk_size:
                    file_count += 1
                    chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                    with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                        chunk_file.writelines(lines)
                    chunk_files.append(chunk_filename)
                    lines = []
            
            if lines:
                file_count += 1
                chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                    chunk_file.writelines(lines)
                chunk_files.append(chunk_filename)

        print(f"信息: 已成功将文件 '{os.path.basename(filepath)}' 分割成 {len(chunk_files)} 个分片。")
        return chunk_files
    except Exception as e:
        print(f"错误: 文件分割过程中失败。原因: {e}")
        return []

def cleanup_temp_chunks(temp_dir: str):
    """清理临时分片目录。"""
    if os.path.exists(temp_dir):
        try:
            shutil.rmtree(temp_dir)
            print(f"信息: 已成功清理临时分片目录: {temp_dir}")
        except OSError as e:
            print(f"错误: 清理临时目录 {temp_dir} 失败。原因: {e}")
