# core/utils.py
import os
import csv
import chardet
import pandas as pd
from typing import Dict, List, Union
import shutil

def merge_xlsx_files(file_list: List[str], output_path: str):
    """
    将多个XLSX文件合并成一个单独的XLSX文件。
    """
    if not file_list:
        print("警告: 没有提供任何文件用于合并。")
        return

    all_dfs = []
    print("信息: 开始合并所有分片扫描结果...")
    for f in file_list:
        try:
            df = pd.read_excel(f)
            all_dfs.append(df)
            print(f" - 已读取 {os.path.basename(f)} ({len(df)}行)")
        except Exception as e:
            print(f"错误: 读取文件 {f} 失败，跳过此文件。原因: {e}")

    if not all_dfs:
        print("错误: 未能从任何分片结果中读取到数据，无法生成最终报告。")
        return

    # 合并所有数据
    final_df = pd.concat(all_dfs, ignore_index=True)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True)

    # 保存到最终的XLSX文件
    try:
        final_df.to_excel(output_path, index=False)
        print(f"\n成功: 所有分片结果已合并完成，并保存到: {output_path}")
        print(f"总计合并了 {len(final_df)} 条记录。")
    except Exception as e:
        print(f"错误: 保存最终合并的XLSX文件失败。原因: {e}")

# 此函数可直接从另一个项目中复制
def load_ip_info(csv_path: str) -> Dict[str, dict]:
    """从CSV文件中加载IP负责人信息到字典中。"""
    ip_info = {}
    if not os.path.exists(csv_path):
        print(f"警告: IP信息CSV文件在 {csv_path} 未找到。负责人信息将为空。")
        return ip_info
    try:
        with open(csv_path, 'rb') as f:
            raw_data = f.read(50000)
            encoding_result = chardet.detect(raw_data)
        with open(csv_path, 'r', encoding=encoding_result['encoding'], errors='ignore') as f:
            reader = csv.DictReader(f)
            for row in reader:
                ip = row.get('ip')
                if ip:
                    ip_info[ip] = {
                        'bg': row.get('bg', ''), 'dept': row.get('dept', ''), 'center': row.get('center', ''),
                        "oa_group": row.get('oa_group', ''), "user_name": row.get('user_name', '')
                    }
        print(f"信息: 成功加载 {len(ip_info)} 条IP负责人记录。")
    except Exception as e:
        print(f"错误: 加载IP信息CSV失败。原因: {str(e)}")
    return ip_info

def find_latest_excel_report(folder_path: str) -> Union[str, None]:
    """在一个目录中查找最新修改的 .xlsx 文件。"""
    try:
        xlsx_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.xlsx')]
        if not xlsx_files:
            print(f"错误: 目录 {folder_path} 中没有找到 .xlsx 文件。")
            return None
        
        full_paths = [os.path.join(folder_path, f) for f in xlsx_files]
        latest_file = max(full_paths, key=os.path.getmtime)
        print(f"信息: 找到待处理的最新报告: {os.path.basename(latest_file)}")
        return latest_file
    except FileNotFoundError:
        print(f"错误: 报告目录 {folder_path} 未找到。")
        return None

def split_file_into_chunks(filepath: str, chunk_size: int, temp_dir: str) -> List[str]:
    """
    将一个大文本文件按指定的行数分割成多个小文件（分片）。
    """
    if not os.path.exists(filepath):
        print(f"错误: 输入文件不存在: {filepath}")
        return []
        
    if os.path.exists(temp_dir):
        cleanup_temp_chunks(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)

    chunk_files = []
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            file_count = 0
            lines = []
            for line in f:
                lines.append(line)
                if len(lines) >= chunk_size:
                    file_count += 1
                    chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                    with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                        chunk_file.writelines(lines)
                    chunk_files.append(chunk_filename)
                    lines = []
            
            if lines:
                file_count += 1
                chunk_filename = os.path.join(temp_dir, f"chunk_{file_count}.txt")
                with open(chunk_filename, 'w', encoding='utf-8') as chunk_file:
                    chunk_file.writelines(lines)
                chunk_files.append(chunk_filename)

        print(f"信息: 已成功将文件 '{os.path.basename(filepath)}' 分割成 {len(chunk_files)} 个分片。")
        return chunk_files
    except Exception as e:
        print(f"错误: 文件分割过程中失败。原因: {e}")
        return []

def cleanup_temp_chunks(temp_dir: str):
    """清理临时分片目录。"""
    if os.path.exists(temp_dir):
        try:
            shutil.rmtree(temp_dir)
            print(f"信息: 已成功清理临时分片目录: {temp_dir}")
        except OSError as e:
            print(f"错误: 清理临时目录 {temp_dir} 失败。原因: {e}")
