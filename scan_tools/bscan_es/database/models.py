# database/models.py
from dataclasses import dataclass, field
from datetime import datetime
from ..config import settings

@dataclass
class Vulnerability:
    """用于表示一个ElasticSearch未授权访问漏洞的数据模型。"""
    host: str
    port: int
    url: str

    # --- 从 settings.py 加载的固定信息 ---
    risk_type: int = field(default=settings.ELASTICSEARCH_VULN_DETAILS['risk_id'], init=False)
    risk_name: str = field(default=settings.ELASTICSEARCH_VULN_DETAILS['risk_type'], init=False)
    risk_level: str = field(default=settings.ELASTICSEARCH_VULN_DETAILS['risk_level'], init=False)
    risk_tag: str = field(default="未授权访问", init=False)
    create_by: str = field(default=settings.VULN_CREATOR, init=False)
    risk_status: str = field(default="2", init=False) # 活跃状态

    # --- 动态生成的字段 ---
    date_key: str = field(init=False)
    create_time: str = field(init=False)
    case_content: str = field(init=False)
    guide_link: str = field(default="https://iwiki.woa.com/p/4007424247", init=False)

    @property
    def risk_desc(self) -> str:
        return f"高危：发现ElasticSearch数据库存在未授权访问漏洞，位于 {self.host}:{self.port}。攻击者可利用此漏洞直接访问、查询、修改或删除敏感数据，对业务造成严重影响。修复指引：为ElasticSearch服务添加身份验证并限制公网访问。"

    def __post_init__(self):
        """在对象创建后自动填充动态字段"""
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""

    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组 (20个字段)。"""
        return (
            self.date_key,
            "内网web漏洞",  # leak_type, 可根据需要调整
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )
