# config/settings.py
from datetime import datetime
# 使用绝对路径从项目根目录导入共享配置
from config.common_config import *

# ==============================================================================
# 基础配置 (项目特有)
# ==============================================================================
# 项目根目录
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# 工作目录
WORKSPACE_DIR = os.path.join(BASE_DIR, "assets")
# 新增: fscan 扫描日志的存放目录
LOG_DIR = os.path.join(WORKSPACE_DIR, 'log')
os.makedirs(LOG_DIR, exist_ok=True)


# ==============================================================================
# 漏洞与扫描配置 (项目特有)
# ==============================================================================
# 漏洞类型定义
RISK_TYPE_REDIS = 4
REDIS_VULN_DETAILS = {
    'risk_id': RISK_TYPE_REDIS,
    'risk_type': 'Redis未授权访问',
    'risk_level': '3', # 未授权访问通常是高危
    'risk_source': 'fscan',
}

# fscan (Redis未授权扫描器) 相关配置
FSCAN_EXECUTABLE = os.path.join(BASE_DIR, "fscan")
FSCAN_PORTS = "5555,6379,27017,27018"
FSCAN_THREADS = 300 # 新增：扫描线程数

# 原始扫描相关配置
RAW_TARGETS_FILE = IP_INFO_PATH  # 使用从common_config导入的路径
IP_CHUNK_SIZE = 100000  # 原始IP文件分片大小
TEMP_CHUNK_DIR = os.path.join(BASE_DIR, 'temp_chunks')

# 复测扫描相关配置
# 注意：复测目标文件是临时的，放在 workspace 目录即可
AUDIT_TARGETS_FILE = os.path.join(WORKSPACE_DIR, f"redis_audit_targets_{datetime.now().strftime('%Y%m%d')}.txt")

# ==============================================================================
# 定时任务配置 (项目特有)
# ==============================================================================
# 每日执行扫描任务的时间 (24小时制)


# --- 漏洞信息配置 ---
VULN_CREATOR = "oldbuddyxin"
